import express from 'express';
import {
  getMessages,
  getConversations,
  getConversationById,
  getMessagesByConversation,
  createMessage,
  markMessageAsRead,
  deleteMessage,
  createConversation
} from '../controllers/messageController.js';
import { authMiddleware } from '../middleware/authMiddleware.js';

const router = express.Router();

// All routes require authentication
router.use(authMiddleware);

// Get all messages for the current user
router.get('/', getMessages);

// Send a new message
router.post('/', createMessage);

// Get all conversations for the current user
router.get('/conversations', getConversations);

// Get specific conversation by ID
router.get('/conversations/:conversationId', getConversationById);

// Create a new conversation
router.post('/conversations', createConversation);

// Get messages for a specific conversation
router.get('/conversations/:conversationId/messages', getMessagesByConversation);

// Send a new message to a conversation
router.post('/conversations/:conversationId/messages', createMessage);

// Mark message as read
router.put('/messages/:messageId/read', markMessageAsRead);

// Delete message (soft delete for the current user)
router.delete('/messages/:messageId', deleteMessage);

export default router; 