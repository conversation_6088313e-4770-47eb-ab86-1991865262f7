import Bid from '../models/bidModel.js';
import Project from '../models/projectModel.js';
import asyncHandler from '../utils/asyncHandler.js';
import { 
  calculateCompetitiveAnalysis, 
  calculateOptimizationScore 
} from '../utils/priceOptimization.js';

// @desc    Create a new bid
// @route   POST /api/bids
// @access  Private (Vendor only)
export const createBid = asyncHandler(async (req, res) => {
  const { 
    project: projectId, 
    amount, 
    proposal, 
    deliveryTime, 
    milestones,
    attachments 
  } = req.body;

  // Validate required fields
  if (!projectId || !amount || !proposal || !deliveryTime) {
    res.status(400);
    throw new Error('Please provide all required fields');
  }

  // Check if project exists
  const project = await Project.findById(projectId);
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }

  // Check if project is open for bidding
  if (project.status !== 'open') {
    res.status(400);
    throw new Error('This project is not open for bidding');
  }

  // Check if vendor already has a bid for this project
  const existingBid = await Bid.findOne({ 
    project: projectId, 
    vendor: req.user._id,
    status: { $ne: 'withdrawn' }
  });

  if (existingBid) {
    res.status(400);
    throw new Error('You already have an active bid for this project');
  }

  // Get all bids for this project for competitive analysis
  const projectBids = await Bid.find({ 
    project: projectId,
    status: { $ne: 'withdrawn' }
  });

  // Calculate competitive analysis
  const competitiveAnalysis = await calculateCompetitiveAnalysis(
    { amount },
    projectBids.map(bid => ({ amount: bid.amount }))
  );

  // Create new bid
  const newBid = await Bid.create({
    project: projectId,
    vendor: req.user._id,
    amount,
    proposal,
    deliveryTime,
    milestones: milestones || [],
    attachments: attachments || [],
    competitiveAnalysis
  });

  // Calculate optimization score
  const optimizationScore = await calculateOptimizationScore(
    { 
      amount, 
      deliveryTime, 
      proposal, 
      vendor: req.user 
    },
    project,
    competitiveAnalysis
  );

  // Update bid with optimization score
  newBid.optimizationScore = optimizationScore;
  await newBid.save();

  // Populate vendor info
  await newBid.populate('vendor', 'name email company companyLogo rating');

  res.status(201).json(newBid);
});

// @desc    Get all bids (for admin or user's own bids)
// @route   GET /api/bids
// @access  Private
export const getBids = asyncHandler(async (req, res) => {
  const pageSize = parseInt(req.query.limit) || 20;
  const page = parseInt(req.query.page) || 1;

  // Build filter object
  const filter = {};

  // Role-based filtering
  if (req.user.role === 'admin') {
    // Admin can see all bids
    // No additional filtering needed
  } else if (req.user.role === 'vendor') {
    // Vendors can only see their own bids
    filter.vendor = req.user._id;
  } else if (req.user.role === 'client') {
    // Clients can see bids on their projects
    const userProjects = await Project.find({ client: req.user._id }).select('_id');
    const projectIds = userProjects.map(p => p._id);
    filter.project = { $in: projectIds };
  } else {
    res.status(403);
    throw new Error('Not authorized to view bids');
  }

  // Filter by status if provided
  if (req.query.status) {
    filter.status = req.query.status;
  }

  // Filter by project if provided
  if (req.query.project) {
    filter.project = req.query.project;
  }

  const count = await Bid.countDocuments(filter);

  const bids = await Bid.find(filter)
    .populate('vendor', 'name email company companyLogo rating')
    .populate('project', 'title budget status client')
    .sort({ createdAt: -1 })
    .skip(pageSize * (page - 1))
    .limit(pageSize);

  res.json(bids);
});

// @desc    Get all bids for a project
// @route   GET /api/bids/project/:projectId or GET /api/projects/:id/bids
// @access  Private
export const getProjectBids = asyncHandler(async (req, res) => {
  const { projectId, id } = req.params;
  const targetProjectId = projectId || id;

  // Check if project exists
  const project = await Project.findById(targetProjectId);
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }

  // Check authorization
  const isProjectOwner = project.client.toString() === req.user._id.toString();
  const isVendorWithBid = await Bid.exists({
    project: targetProjectId,
    vendor: req.user._id
  });
  const isAdmin = req.user.role === 'admin';

  // If not authorized, return error
  if (!isProjectOwner && !isVendorWithBid && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to view bids for this project');
  }

  // Get all bids for the project
  const bids = await Bid.find({ project: targetProjectId })
    .populate('vendor', 'name email company companyLogo rating')
    .sort({ createdAt: -1 });

  // Add extra data for project owner
  if (isProjectOwner || isAdmin) {
    res.json(bids);
  } else {
    // For vendors, only return their own bids
    const filteredBids = bids.filter(bid => 
      bid.vendor._id.toString() === req.user._id.toString()
    );
    res.json(filteredBids);
  }
});

// @desc    Get a bid by ID
// @route   GET /api/bids/:id
// @access  Private
export const getBidById = asyncHandler(async (req, res) => {
  const bid = await Bid.findById(req.params.id)
    .populate('vendor', 'name email company companyLogo rating')
    .populate('project');

  if (!bid) {
    res.status(404);
    throw new Error('Bid not found');
  }

  // Check authorization
  const isProjectOwner = bid.project.client.toString() === req.user._id.toString();
  const isBidOwner = bid.vendor._id.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';

  if (!isProjectOwner && !isBidOwner && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to view this bid');
  }

  res.json(bid);
});

// @desc    Update bid status (accept/reject)
// @route   PUT /api/bids/:id/status
// @access  Private (Project Owner only)
export const updateBidStatus = asyncHandler(async (req, res) => {
  const { status } = req.body;

  if (!status || !['accepted', 'rejected'].includes(status)) {
    res.status(400);
    throw new Error('Please provide a valid status (accepted or rejected)');
  }

  const bid = await Bid.findById(req.params.id);

  if (!bid) {
    res.status(404);
    throw new Error('Bid not found');
  }

  // Get the project
  const project = await Project.findById(bid.project);

  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }

  // Check if user is the project owner
  if (project.client.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to update this bid status');
  }

  // Check if project is still open
  if (project.status !== 'open' && status === 'accepted') {
    res.status(400);
    throw new Error('This project is not open for accepting bids');
  }

  // If accepting, reject all other bids
  if (status === 'accepted') {
    await Bid.updateMany(
      { 
        project: project._id, 
        _id: { $ne: bid._id },
        status: 'pending'
      },
      { status: 'rejected' }
    );

    // Update the project status and assigned vendor
    project.status = 'in-progress';
    project.assignedVendor = bid.vendor;
    project.winningBid = bid._id;
    await project.save();
  }

  // Update bid status
  bid.status = status;
  await bid.save();

  res.json(bid);
});

// @desc    Update a bid
// @route   PUT /api/bids/:id
// @access  Private (Bid Owner only)
export const updateBid = asyncHandler(async (req, res) => {
  const { 
    amount, 
    proposal, 
    deliveryTime, 
    milestones,
    attachments 
  } = req.body;

  const bid = await Bid.findById(req.params.id);

  if (!bid) {
    res.status(404);
    throw new Error('Bid not found');
  }

  // Check if user is the bid owner
  if (bid.vendor.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to update this bid');
  }

  // Check if bid can be updated
  if (bid.status !== 'pending') {
    res.status(400);
    throw new Error('Cannot update a bid that has been accepted, rejected, or withdrawn');
  }

  // Update bid
  bid.amount = amount || bid.amount;
  bid.proposal = proposal || bid.proposal;
  bid.deliveryTime = deliveryTime || bid.deliveryTime;
  bid.milestones = milestones || bid.milestones;
  bid.attachments = attachments || bid.attachments;
  bid.revisions += 1;

  // Get project and all bids for re-calculating analysis
  const project = await Project.findById(bid.project);
  const projectBids = await Bid.find({ 
    project: bid.project,
    status: { $ne: 'withdrawn' }
  });

  // Recalculate competitive analysis
  const competitiveAnalysis = await calculateCompetitiveAnalysis(
    { amount: bid.amount },
    projectBids.map(b => ({ amount: b.amount }))
  );
  bid.competitiveAnalysis = competitiveAnalysis;

  // Recalculate optimization score
  const optimizationScore = await calculateOptimizationScore(
    { 
      amount: bid.amount, 
      deliveryTime: bid.deliveryTime, 
      proposal: bid.proposal, 
      vendor: req.user 
    },
    project,
    competitiveAnalysis
  );
  bid.optimizationScore = optimizationScore;

  const updatedBid = await bid.save();
  
  res.json(updatedBid);
});

// @desc    Withdraw a bid
// @route   PUT /api/bids/:id/withdraw
// @access  Private (Bid Owner only)
export const withdrawBid = asyncHandler(async (req, res) => {
  const bid = await Bid.findById(req.params.id);

  if (!bid) {
    res.status(404);
    throw new Error('Bid not found');
  }

  // Check if user is the bid owner
  if (bid.vendor.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to withdraw this bid');
  }

  // Check if bid can be withdrawn
  if (bid.status !== 'pending') {
    res.status(400);
    throw new Error('Cannot withdraw a bid that has been accepted or rejected');
  }

  // Update bid status
  bid.status = 'withdrawn';
  await bid.save();

  res.json({ message: 'Bid withdrawn successfully', bid });
});

// @desc    Create a counter offer
// @route   POST /api/bids/:id/counter
// @access  Private (Project Owner only)
export const createCounterOffer = asyncHandler(async (req, res) => {
  const { amount, proposal, deliveryTime, milestones } = req.body;

  if (!amount && !proposal && !deliveryTime && !milestones) {
    res.status(400);
    throw new Error('Please provide at least one field to update');
  }

  const originalBid = await Bid.findById(req.params.id)
    .populate('project');

  if (!originalBid) {
    res.status(404);
    throw new Error('Original bid not found');
  }

  // Check if user is the project owner
  if (originalBid.project.client.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to create a counter offer');
  }

  // Create counter offer as a new bid
  const counterOffer = new Bid({
    project: originalBid.project._id,
    vendor: originalBid.vendor,
    amount: amount || originalBid.amount,
    proposal: proposal || originalBid.proposal,
    deliveryTime: deliveryTime || originalBid.deliveryTime,
    milestones: milestones || originalBid.milestones,
    isCounterOffer: true,
    originalBid: originalBid._id,
    attachments: originalBid.attachments
  });

  const savedCounterOffer = await counterOffer.save();
  
  // Update original bid's status if it's pending
  if (originalBid.status === 'pending') {
    originalBid.status = 'rejected';
    await originalBid.save();
  }

  res.status(201).json(savedCounterOffer);
}); 