import { Message, Conversation } from '../models/messageModel.js';
import User from '../models/userModel.js';
import Project from '../models/projectModel.js';
import asyncHandler from '../utils/asyncHandler.js';
import { uploadFileToCloudinary } from '../utils/cloudinaryUpload.js';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// @desc    Get all messages for the current user
// @route   GET /api/messages
// @access  Private
export const getMessages = asyncHandler(async (req, res) => {
  const pageSize = parseInt(req.query.limit) || 20;
  const page = parseInt(req.query.page) || 1;
  const userId = req.user._id;

  // Build filter for search functionality
  const filter = {};

  if (req.query.search) {
    filter.content = { $regex: req.query.search, $options: 'i' };
  }

  if (req.query.since) {
    filter.createdAt = { $gte: new Date(req.query.since) };
  }

  // Find all conversations where the user is a participant
  const userConversations = await Conversation.find({
    participants: userId,
    isActive: true
  }).select('_id');

  const conversationIds = userConversations.map(conv => conv._id);

  // Get messages from user's conversations that haven't been deleted for this user
  const messages = await Message.find({
    conversation: { $in: conversationIds },
    deletedFor: { $ne: userId },
    ...filter
  })
    .populate('sender', 'name email profileImage role')
    .populate('conversation', 'participants project title type')
    .sort({ createdAt: -1 })
    .skip(pageSize * (page - 1))
    .limit(pageSize);

  // Add recipient field for backward compatibility with tests
  const messagesWithRecipient = messages.map(message => {
    const messageObj = message.toObject();

    // Find the recipient (the participant who is not the sender)
    if (message.conversation && message.conversation.participants) {
      const recipient = message.conversation.participants.find(
        participant => participant.toString() !== message.sender._id.toString()
      );
      messageObj.recipient = recipient;
    }

    return messageObj;
  });

  // Get total count for pagination
  const totalMessages = await Message.countDocuments({
    conversation: { $in: conversationIds },
    deletedFor: { $ne: userId },
    ...filter
  });

  res.json(messagesWithRecipient);
});

// @desc    Get all conversations for the current user
// @route   GET /api/messages/conversations
// @access  Private
export const getConversations = asyncHandler(async (req, res) => {
  const userId = req.user._id;
  
  // Find all conversations where the user is a participant
  const conversations = await Conversation.find({
    participants: userId,
    isActive: true
  })
    .populate('participants', 'name email profileImage role company')
    .populate('project', 'title status')
    .populate({
      path: 'lastMessage',
      select: 'content createdAt sender attachments readBy',
      populate: {
        path: 'sender',
        select: 'name email profileImage'
      }
    })
    .sort({ updatedAt: -1 });
  
  // Get unread count for displaying in the UI
  const totalUnreadCount = await Conversation.getUnreadCountForUser(userId);
  
  res.json({
    conversations,
    totalUnreadCount
  });
});

// @desc    Get a specific conversation by ID
// @route   GET /api/messages/conversations/:conversationId
// @access  Private
export const getConversationById = asyncHandler(async (req, res) => {
  const { conversationId } = req.params;
  const userId = req.user._id;
  
  // Find the conversation and check if the user is a participant
  const conversation = await Conversation.findById(conversationId)
    .populate('participants', 'name email profileImage role company')
    .populate('project', 'title status client assignedVendor')
    .populate({
      path: 'lastMessage',
      select: 'content createdAt sender attachments readBy',
      populate: {
        path: 'sender',
        select: 'name email profileImage'
      }
    });
  
  if (!conversation) {
    res.status(404);
    throw new Error('Conversation not found');
  }
  
  // Check if the user is a participant
  const isParticipant = conversation.participants.some(
    participant => participant._id.toString() === userId.toString()
  );
  
  if (!isParticipant && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to access this conversation');
  }
  
  // Reset unread count for the user
  await conversation.resetUnreadCountForUser(userId);
  
  res.json(conversation);
});

// @desc    Create a new conversation
// @route   POST /api/messages/conversations
// @access  Private
export const createConversation = asyncHandler(async (req, res) => {
  const { participantIds, projectId, title, type } = req.body;
  const userId = req.user._id;
  
  // Validate required fields
  if (!participantIds || !Array.isArray(participantIds) || participantIds.length === 0) {
    res.status(400);
    throw new Error('Please provide at least one participant');
  }
  
  // Add current user to participants if not included
  if (!participantIds.includes(userId.toString())) {
    participantIds.push(userId.toString());
  }
  
  // Verify all participants exist
  const participants = await User.find({ _id: { $in: participantIds } });
  if (participants.length !== participantIds.length) {
    res.status(400);
    throw new Error('One or more participants do not exist');
  }
  
  // If project is specified, verify it exists and user has access
  let project = null;
  if (projectId) {
    project = await Project.findById(projectId);
    if (!project) {
      res.status(404);
      throw new Error('Project not found');
    }
    
    // Check if the user is associated with the project
    const isProjectOwner = project.client.toString() === userId.toString();
    const isAssignedVendor = project.assignedVendor && 
      project.assignedVendor.toString() === userId.toString();
    const isAdmin = req.user.role === 'admin';
    
    if (!isProjectOwner && !isAssignedVendor && !isAdmin) {
      res.status(403);
      throw new Error('Not authorized to create a conversation for this project');
    }
  }
  
  // For direct conversations (between 2 people), check if one already exists
  if (type === 'direct' && participantIds.length === 2) {
    const existingConversation = await Conversation.findOne({
      participants: { $all: participantIds },
      participants: { $size: 2 },
      type: 'direct',
      isActive: true
    });
    
    if (existingConversation) {
      // Return the existing conversation
      const populatedConversation = await Conversation.findById(existingConversation._id)
        .populate('participants', 'name email profileImage role company')
        .populate('project', 'title status')
        .populate({
          path: 'lastMessage',
          select: 'content createdAt sender attachments readBy',
          populate: {
            path: 'sender',
            select: 'name email profileImage'
          }
        });
      
      return res.json(populatedConversation);
    }
  }
  
  // Create the new conversation
  const conversation = await Conversation.create({
    participants: participantIds,
    project: projectId,
    title: title || '',
    type: type || 'direct',
    unreadCount: participantIds.reduce((acc, participantId) => {
      acc.set(participantId.toString(), 0);
      return acc;
    }, new Map())
  });
  
  // Populate and return
  const populatedConversation = await Conversation.findById(conversation._id)
    .populate('participants', 'name email profileImage role company')
    .populate('project', 'title status');
  
  // Emit socket event to all participants for new conversation
  const io = req.app.get('io');
  participantIds.forEach(participantId => {
    io.to(`user:${participantId}`).emit('new_conversation', populatedConversation);
  });
  
  res.status(201).json(populatedConversation);
});

// @desc    Get messages for a specific conversation
// @route   GET /api/messages/conversations/:conversationId/messages
// @access  Private
export const getMessagesByConversation = asyncHandler(async (req, res) => {
  const { conversationId } = req.params;
  const { page = 1, limit = 20 } = req.query;
  const userId = req.user._id;
  
  // Verify conversation exists
  const conversation = await Conversation.findById(conversationId);
  if (!conversation) {
    res.status(404);
    throw new Error('Conversation not found');
  }
  
  // Check if the user is a participant
  const isParticipant = conversation.participants.some(
    participant => participant.toString() === userId.toString()
  );
  
  if (!isParticipant && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to access this conversation');
  }
  
  // Calculate pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);
  
  // Get messages that haven't been deleted for this user
  const messages = await Message.find({
    conversation: conversationId,
    deletedFor: { $ne: userId }
  })
    .populate('sender', 'name email profileImage role')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));
  
  // Get total count for pagination
  const totalMessages = await Message.countDocuments({
    conversation: conversationId,
    deletedFor: { $ne: userId }
  });
  
  // Generate signed URLs for attachments
  const processedMessages = await Promise.all(messages.map(async (message) => {
    const messageObj = message.toObject();
    
    if (messageObj.attachments && messageObj.attachments.length > 0) {
      messageObj.attachments = messageObj.attachments.map(attachment => {
        // For Cloudinary, we already have the URL stored
        return {
          ...attachment,
          url: attachment.url || attachment.fileKey // fallback to fileKey if url not stored
        };
      });
    }
    
    return messageObj;
  }));
  
  // Mark messages as read
  await Message.updateMany(
    {
      conversation: conversationId,
      sender: { $ne: userId },
      'readBy.user': { $ne: userId }
    },
    {
      $push: {
        readBy: {
          user: userId,
          readAt: new Date()
        }
      }
    }
  );
  
  // Reset unread count for the user
  await conversation.resetUnreadCountForUser(userId);
  
  res.json({
    messages: processedMessages,
    totalMessages,
    currentPage: parseInt(page),
    totalPages: Math.ceil(totalMessages / parseInt(limit))
  });
});

// @desc    Send a new message to a conversation
// @route   POST /api/messages/conversations/:conversationId/messages
// @access  Private
export const createMessage = asyncHandler(async (req, res) => {
  const { conversationId } = req.params;
  const { content, attachmentIds } = req.body;
  const userId = req.user._id;
  
  // Validate required fields
  if (!content && (!attachmentIds || attachmentIds.length === 0)) {
    res.status(400);
    throw new Error('Message must contain content or attachments');
  }
  
  // Verify conversation exists
  const conversation = await Conversation.findById(conversationId);
  if (!conversation) {
    res.status(404);
    throw new Error('Conversation not found');
  }
  
  // Check if the user is a participant
  const isParticipant = conversation.participants.some(
    participant => participant.toString() === userId.toString()
  );
  
  if (!isParticipant && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to send messages to this conversation');
  }
  
  // Handle file attachments (if any)
  let attachments = [];
  if (req.files && req.files.length > 0) {
    attachments = await Promise.all(req.files.map(async (file) => {
      // Upload to Cloudinary
      const uploadResult = await uploadFileToCloudinary(
        {
          buffer: fs.readFileSync(file.path),
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size
        },
        `messages/${conversationId}`
      );

      // Clean up the local file
      fs.unlinkSync(file.path);

      return {
        fileKey: uploadResult.key,
        fileName: file.originalname,
        fileType: file.mimetype,
        fileSize: file.size,
        url: uploadResult.url
      };
    }));
  }
  
  // Create the message
  const message = await Message.create({
    conversation: conversationId,
    sender: userId,
    content: content || '',
    attachments,
    readBy: [{ user: userId }] // Mark as read by sender
  });
  
  // Update conversation with last message info and unread counts
  await conversation.updateUnreadCounts(userId, message._id);
  
  // Populate message before sending response
  const populatedMessage = await Message.findById(message._id)
    .populate('sender', 'name email profileImage role');
  
  // Generate signed URLs for attachments if needed
  const processedMessage = populatedMessage.toObject();
  if (processedMessage.attachments && processedMessage.attachments.length > 0) {
    processedMessage.attachments = processedMessage.attachments.map(attachment => {
      // For Cloudinary, we already have the URL stored
      return {
        ...attachment,
        url: attachment.url || attachment.fileKey // fallback to fileKey if url not stored
      };
    });
  }
  
  // Emit socket event to all participants
  const io = req.app.get('io');
  conversation.participants.forEach(participantId => {
    if (participantId.toString() !== userId.toString()) {
      io.to(`user:${participantId}`).emit('new_message', {
        message: processedMessage,
        conversation: conversation._id
      });
    }
  });
  
  res.status(201).json(processedMessage);
});

// @desc    Mark a message as read
// @route   PUT /api/messages/messages/:messageId/read
// @access  Private
export const markMessageAsRead = asyncHandler(async (req, res) => {
  const { messageId } = req.params;
  const userId = req.user._id;
  
  // Find the message
  const message = await Message.findById(messageId);
  if (!message) {
    res.status(404);
    throw new Error('Message not found');
  }
  
  // Get the conversation to check if user is a participant
  const conversation = await Conversation.findById(message.conversation);
  if (!conversation) {
    res.status(404);
    throw new Error('Conversation not found');
  }
  
  // Check if the user is a participant
  const isParticipant = conversation.participants.some(
    participant => participant.toString() === userId.toString()
  );
  
  if (!isParticipant && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to access this message');
  }
  
  // Check if already read by this user
  const alreadyRead = message.readBy.some(
    readRecord => readRecord.user.toString() === userId.toString()
  );
  
  if (!alreadyRead) {
    // Mark as read
    message.readBy.push({
      user: userId,
      readAt: new Date()
    });
    
    await message.save();
    
    // Update conversation unread count
    await conversation.resetUnreadCountForUser(userId);
    
    // Emit socket event for message read status
    const io = req.app.get('io');
    conversation.participants.forEach(participantId => {
      io.to(`user:${participantId}`).emit('message_read', {
        messageId: message._id,
        userId,
        conversationId: conversation._id
      });
    });
  }
  
  res.json({ success: true });
});

// @desc    Delete a message (soft delete for the current user)
// @route   DELETE /api/messages/messages/:messageId
// @access  Private
export const deleteMessage = asyncHandler(async (req, res) => {
  const { messageId } = req.params;
  const userId = req.user._id;
  
  // Find the message
  const message = await Message.findById(messageId);
  if (!message) {
    res.status(404);
    throw new Error('Message not found');
  }
  
  // Check if the user is the sender or admin (for full deletion rights)
  const isSender = message.sender.toString() === userId.toString();
  const isAdmin = req.user.role === 'admin';
  
  // Get the conversation to check if user is a participant
  const conversation = await Conversation.findById(message.conversation);
  if (!conversation) {
    res.status(404);
    throw new Error('Conversation not found');
  }
  
  // Check if the user is a participant
  const isParticipant = conversation.participants.some(
    participant => participant.toString() === userId.toString()
  );
  
  if (!isParticipant && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to delete this message');
  }
  
  // If user is sender or admin and message was sent less than 10 minutes ago,
  // they can completely delete the message (but we'll still soft delete)
  if ((isSender || isAdmin) && 
      (Date.now() - message.createdAt.getTime() < 10 * 60 * 1000)) {
    // Add to deletedFor list for this user
    if (!message.deletedFor.includes(userId)) {
      message.deletedFor.push(userId);
      await message.save();
    }
    
    // Emit socket event for message deletion
    const io = req.app.get('io');
    conversation.participants.forEach(participantId => {
      io.to(`user:${participantId}`).emit('message_deleted', {
        messageId: message._id,
        conversationId: conversation._id,
        deletedFor: userId
      });
    });
    
    return res.json({ 
      success: true, 
      message: 'Message deleted successfully for you'
    });
  }
  
  // Otherwise, just soft delete for the current user
  if (!message.deletedFor.includes(userId)) {
    message.deletedFor.push(userId);
    await message.save();
  }
  
  res.json({ 
    success: true, 
    message: 'Message removed from your view'
  });
}); 