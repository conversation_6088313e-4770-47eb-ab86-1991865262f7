{"summary": {"total": 8, "passed": 5, "failed": 3, "successRate": "62.50"}, "tests": [{"name": "User Management", "description": "Test user management and administration features", "startTime": "2025-07-21T12:27:14.065Z", "logs": [{"timestamp": "2025-07-21T12:27:14.065Z", "type": "test", "message": "Starting test: User Management - Test user management and administration features"}, {"timestamp": "2025-07-21T12:27:14.068Z", "type": "error", "message": "Request failed: GET /admin/users - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.068Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.069Z", "type": "error", "message": "Test FAILED: User Management"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:14.068Z", "duration": 3}, {"name": "Role Assignment", "description": "Test user role management and permissions", "startTime": "2025-07-21T12:27:14.070Z", "logs": [{"timestamp": "2025-07-21T12:27:14.070Z", "type": "test", "message": "Starting test: Role Assignment - Test user role management and permissions"}, {"timestamp": "2025-07-21T12:27:14.072Z", "type": "error", "message": "Request failed: GET /admin/users - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.073Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.073Z", "type": "error", "message": "Test FAILED: Role Assignment"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:14.073Z", "duration": 3}, {"name": "Platform Analytics", "description": "Test platform-wide analytics and reporting", "startTime": "2025-07-21T12:27:14.074Z", "logs": [{"timestamp": "2025-07-21T12:27:14.074Z", "type": "test", "message": "Starting test: Platform Analytics - Test platform-wide analytics and reporting"}, {"timestamp": "2025-07-21T12:27:14.078Z", "type": "error", "message": "Request failed: GET /admin/analytics/overview - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.078Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.079Z", "type": "error", "message": "Test FAILED: Platform Analytics"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:14.078Z", "duration": 4}, {"name": "System Configuration", "description": "Test system settings and configuration management", "startTime": "2025-07-21T12:27:14.079Z", "logs": [{"timestamp": "2025-07-21T12:27:14.079Z", "type": "test", "message": "Starting test: System Configuration - Test system settings and configuration management"}, {"timestamp": "2025-07-21T12:27:14.082Z", "type": "error", "message": "Request failed: GET /admin/config - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.083Z", "type": "info", "message": "System configuration endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.087Z", "type": "error", "message": "Request failed: PUT /admin/config - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.088Z", "type": "info", "message": "System configuration update endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.093Z", "type": "error", "message": "Request failed: GET /admin/features - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.093Z", "type": "info", "message": "Feature flags endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.096Z", "type": "error", "message": "Request failed: PUT /admin/features - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.097Z", "type": "info", "message": "Feature flag update endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.101Z", "type": "error", "message": "Request failed: GET /admin/limits - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.102Z", "type": "info", "message": "System limits endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.102Z", "type": "success", "message": "Test PASSED: System Configuration"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:14.102Z", "duration": 23}, {"name": "Content Moderation", "description": "Test content moderation and security features", "startTime": "2025-07-21T12:27:14.103Z", "logs": [{"timestamp": "2025-07-21T12:27:14.103Z", "type": "test", "message": "Starting test: Content Moderation - Test content moderation and security features"}, {"timestamp": "2025-07-21T12:27:14.108Z", "type": "error", "message": "Request failed: GET /admin/moderation/flagged - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.109Z", "type": "info", "message": "Flagged content endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.113Z", "type": "error", "message": "Request failed: GET /admin/moderation/flagged - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.113Z", "type": "info", "message": "Content approval endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.116Z", "type": "error", "message": "Request failed: GET /admin/reports/users - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.116Z", "type": "info", "message": "User reports endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.122Z", "type": "error", "message": "Request failed: POST /admin/moderation/scan - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.122Z", "type": "info", "message": "Content scanning endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.126Z", "type": "error", "message": "Request failed: GET /admin/moderation/rules - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.126Z", "type": "info", "message": "Content filtering rules endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.127Z", "type": "success", "message": "Test PASSED: Content Moderation"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:14.127Z", "duration": 24}, {"name": "Security Management", "description": "Test security monitoring and management features", "startTime": "2025-07-21T12:27:14.127Z", "logs": [{"timestamp": "2025-07-21T12:27:14.127Z", "type": "test", "message": "Starting test: Security Management - Test security monitoring and management features"}, {"timestamp": "2025-07-21T12:27:14.130Z", "type": "error", "message": "Request failed: GET /admin/security/logs - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.131Z", "type": "info", "message": "Security logs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.135Z", "type": "error", "message": "Request failed: GET /admin/security/failed-logins - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.135Z", "type": "info", "message": "Failed logins endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.140Z", "type": "error", "message": "Request failed: GET /admin/security/blocked-ips - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.141Z", "type": "info", "message": "Blocked IPs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.145Z", "type": "error", "message": "Request failed: GET /admin/security/alerts - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.145Z", "type": "info", "message": "Security alerts endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.146Z", "type": "success", "message": "Test PASSED: Security Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:14.146Z", "duration": 19}, {"name": "System Monitoring", "description": "Test system health and performance monitoring", "startTime": "2025-07-21T12:27:14.147Z", "logs": [{"timestamp": "2025-07-21T12:27:14.147Z", "type": "test", "message": "Starting test: System Monitoring - Test system health and performance monitoring"}, {"timestamp": "2025-07-21T12:27:14.151Z", "type": "error", "message": "Request failed: GET /admin/system/health - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.151Z", "type": "info", "message": "System health endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.156Z", "type": "error", "message": "Request failed: GET /admin/system/performance - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.157Z", "type": "info", "message": "Performance metrics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.162Z", "type": "error", "message": "Request failed: GET /admin/system/errors - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.163Z", "type": "info", "message": "Error logs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.167Z", "type": "error", "message": "Request failed: GET /admin/system/status - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.168Z", "type": "info", "message": "System status endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.168Z", "type": "success", "message": "Test PASSED: System Monitoring"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:14.168Z", "duration": 21}, {"name": "Data Management", "description": "Test data backup, export, and maintenance features", "startTime": "2025-07-21T12:27:14.169Z", "logs": [{"timestamp": "2025-07-21T12:27:14.169Z", "type": "test", "message": "Starting test: Data Management - Test data backup, export, and maintenance features"}, {"timestamp": "2025-07-21T12:27:14.172Z", "type": "error", "message": "Request failed: POST /admin/data/export - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.173Z", "type": "info", "message": "Data export endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.179Z", "type": "error", "message": "Request failed: GET /admin/data/backups - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.179Z", "type": "info", "message": "Backup status endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.184Z", "type": "error", "message": "Request failed: POST /admin/data/cleanup - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.184Z", "type": "info", "message": "Data cleanup endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.187Z", "type": "error", "message": "Request failed: GET /admin/data/statistics - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.188Z", "type": "info", "message": "Database statistics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.189Z", "type": "success", "message": "Test PASSED: Data Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:14.189Z", "duration": 20}], "generatedAt": "2025-07-21T12:27:14.189Z"}