{"summary": {"total": 8, "passed": 7, "failed": 1, "successRate": "87.50"}, "tests": [{"name": "User Management", "description": "Test user management and administration features", "startTime": "2025-07-21T12:42:29.764Z", "logs": [{"timestamp": "2025-07-21T12:42:29.764Z", "type": "test", "message": "Starting test: User Management - Test user management and administration features"}, {"timestamp": "2025-07-21T12:42:29.921Z", "type": "info", "message": "Found 9 total users in system"}, {"timestamp": "2025-07-21T12:42:29.921Z", "type": "info", "message": "User structure validation passed"}, {"timestamp": "2025-07-21T12:42:29.922Z", "type": "info", "message": "User role distribution: {\"client\":3,\"vendor\":5,\"admin\":1}"}, {"timestamp": "2025-07-21T12:42:30.049Z", "type": "info", "message": "User search returned 1 results"}, {"timestamp": "2025-07-21T12:42:30.210Z", "type": "info", "message": "Found 5 vendor users"}, {"timestamp": "2025-07-21T12:42:30.258Z", "type": "error", "message": "Request failed: GET /admin/users - Request failed with status code 403"}, {"timestamp": "2025-07-21T12:42:30.259Z", "type": "info", "message": "User management correctly restricted to admin users"}, {"timestamp": "2025-07-21T12:42:30.260Z", "type": "success", "message": "Test PASSED: User Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:30.260Z", "duration": 496}, {"name": "Role Assignment", "description": "Test user role management and permissions", "startTime": "2025-07-21T12:42:30.261Z", "logs": [{"timestamp": "2025-07-21T12:42:30.261Z", "type": "test", "message": "Starting test: Role Assignment - Test user role management and permissions"}, {"timestamp": "2025-07-21T12:42:30.386Z", "type": "info", "message": "Testing role assignment for user: <PERSON> (current role: vendor)"}, {"timestamp": "2025-07-21T12:42:30.528Z", "type": "info", "message": "Role assignment endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:30.573Z", "type": "error", "message": "Request failed: GET /admin/roles/permissions - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:30.573Z", "type": "info", "message": "Role permissions endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:30.620Z", "type": "error", "message": "Request failed: PUT /admin/users/bulk/role - Request failed with status code 500"}, {"timestamp": "2025-07-21T12:42:30.620Z", "type": "info", "message": "Bulk role assignment endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:30.621Z", "type": "success", "message": "Test PASSED: Role Assignment"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:30.621Z", "duration": 360}, {"name": "Platform Analytics", "description": "Test platform-wide analytics and reporting", "startTime": "2025-07-21T12:42:30.622Z", "logs": [{"timestamp": "2025-07-21T12:42:30.622Z", "type": "test", "message": "Starting test: Platform Analytics - Test platform-wide analytics and reporting"}, {"timestamp": "2025-07-21T12:42:30.665Z", "type": "error", "message": "Request failed: GET /admin/analytics/overview - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:30.666Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:30.668Z", "type": "error", "message": "Test FAILED: Platform Analytics"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:42:30.666Z", "duration": 44}, {"name": "System Configuration", "description": "Test system settings and configuration management", "startTime": "2025-07-21T12:42:30.668Z", "logs": [{"timestamp": "2025-07-21T12:42:30.668Z", "type": "test", "message": "Starting test: System Configuration - Test system settings and configuration management"}, {"timestamp": "2025-07-21T12:42:30.712Z", "type": "error", "message": "Request failed: GET /admin/config - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:30.712Z", "type": "info", "message": "System configuration endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:30.755Z", "type": "error", "message": "Request failed: PUT /admin/config - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:30.756Z", "type": "info", "message": "System configuration update endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:30.828Z", "type": "info", "message": "Feature flags retrieved successfully"}, {"timestamp": "2025-07-21T12:42:30.829Z", "type": "info", "message": "Available features: realTimeMessaging, advancedAnalytics, documentVersioning, aiRecommendations, multiLanguageSupport"}, {"timestamp": "2025-07-21T12:42:30.873Z", "type": "info", "message": "Feature flags updated successfully"}, {"timestamp": "2025-07-21T12:42:30.917Z", "type": "info", "message": "System limits retrieved successfully"}, {"timestamp": "2025-07-21T12:42:30.917Z", "type": "success", "message": "Test PASSED: System Configuration"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:30.917Z", "duration": 249}, {"name": "Content Moderation", "description": "Test content moderation and security features", "startTime": "2025-07-21T12:42:30.919Z", "logs": [{"timestamp": "2025-07-21T12:42:30.919Z", "type": "test", "message": "Starting test: Content Moderation - Test content moderation and security features"}, {"timestamp": "2025-07-21T12:42:30.961Z", "type": "error", "message": "Request failed: GET /admin/moderation/flagged - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:30.961Z", "type": "info", "message": "Flagged content endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.006Z", "type": "error", "message": "Request failed: GET /admin/moderation/flagged - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.007Z", "type": "info", "message": "Content approval endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.052Z", "type": "info", "message": "Found 1 user reports"}, {"timestamp": "2025-07-21T12:42:31.156Z", "type": "info", "message": "Content scan initiated successfully"}, {"timestamp": "2025-07-21T12:42:31.200Z", "type": "error", "message": "Request failed: GET /admin/moderation/rules - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.201Z", "type": "info", "message": "Content filtering rules endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.203Z", "type": "success", "message": "Test PASSED: Content Moderation"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:31.203Z", "duration": 284}, {"name": "Security Management", "description": "Test security monitoring and management features", "startTime": "2025-07-21T12:42:31.204Z", "logs": [{"timestamp": "2025-07-21T12:42:31.204Z", "type": "test", "message": "Starting test: Security Management - Test security monitoring and management features"}, {"timestamp": "2025-07-21T12:42:31.247Z", "type": "error", "message": "Request failed: GET /admin/security/logs - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.248Z", "type": "info", "message": "Security logs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.291Z", "type": "error", "message": "Request failed: GET /admin/security/failed-logins - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.292Z", "type": "info", "message": "Failed logins endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.336Z", "type": "error", "message": "Request failed: GET /admin/security/blocked-ips - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.337Z", "type": "info", "message": "Blocked IPs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.400Z", "type": "error", "message": "Request failed: GET /admin/security/alerts - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.400Z", "type": "info", "message": "Security alerts endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.401Z", "type": "success", "message": "Test PASSED: Security Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:31.401Z", "duration": 197}, {"name": "System Monitoring", "description": "Test system health and performance monitoring", "startTime": "2025-07-21T12:42:31.402Z", "logs": [{"timestamp": "2025-07-21T12:42:31.402Z", "type": "test", "message": "Starting test: System Monitoring - Test system health and performance monitoring"}, {"timestamp": "2025-07-21T12:42:31.445Z", "type": "error", "message": "Request failed: GET /admin/system/health - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.446Z", "type": "info", "message": "System health endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.489Z", "type": "error", "message": "Request failed: GET /admin/system/performance - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.490Z", "type": "info", "message": "Performance metrics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.533Z", "type": "error", "message": "Request failed: GET /admin/system/errors - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.534Z", "type": "info", "message": "Error logs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.577Z", "type": "info", "message": "System status retrieved successfully"}, {"timestamp": "2025-07-21T12:42:31.577Z", "type": "success", "message": "Test PASSED: System Monitoring"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:31.577Z", "duration": 175}, {"name": "Data Management", "description": "Test data backup, export, and maintenance features", "startTime": "2025-07-21T12:42:31.578Z", "logs": [{"timestamp": "2025-07-21T12:42:31.578Z", "type": "test", "message": "Starting test: Data Management - Test data backup, export, and maintenance features"}, {"timestamp": "2025-07-21T12:42:31.622Z", "type": "info", "message": "Data export initiated with ID: export_1753101751624"}, {"timestamp": "2025-07-21T12:42:31.664Z", "type": "info", "message": "Found 2 backup records"}, {"timestamp": "2025-07-21T12:42:31.727Z", "type": "info", "message": "Data cleanup initiated successfully"}, {"timestamp": "2025-07-21T12:42:31.966Z", "type": "info", "message": "Database statistics retrieved successfully"}, {"timestamp": "2025-07-21T12:42:31.967Z", "type": "success", "message": "Test PASSED: Data Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:31.966Z", "duration": 388}], "generatedAt": "2025-07-21T12:42:31.967Z"}