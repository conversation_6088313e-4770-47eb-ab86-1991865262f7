import User from '../models/userModel.js';
import Project from '../models/projectModel.js';
import Bid from '../models/bidModel.js';
import { Message } from '../models/messageModel.js';
import Document from '../models/documentModel.js';
import asyncHandler from '../utils/asyncHandler.js';

// @desc    Get all users for admin management
// @route   GET /api/admin/users
// @access  Private (Admin only)
export const getAdminUsers = asyncHandler(async (req, res) => {
  const pageSize = parseInt(req.query.limit) || 20;
  const page = parseInt(req.query.page) || 1;

  // Build filter object
  const filter = {};
  
  if (req.query.role && req.query.role !== 'all') {
    filter.role = req.query.role;
  }

  if (req.query.search) {
    filter.$or = [
      { name: { $regex: req.query.search, $options: 'i' } },
      { email: { $regex: req.query.search, $options: 'i' } },
      { company: { $regex: req.query.search, $options: 'i' } }
    ];
  }

  const count = await User.countDocuments(filter);

  const users = await User.find(filter)
    .select('-password')
    .sort({ createdAt: -1 })
    .skip(pageSize * (page - 1))
    .limit(pageSize);

  res.json(users);
});

// @desc    Update user role
// @route   PUT /api/admin/users/:userId/role
// @access  Private (Admin only)
export const updateUserRole = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { role } = req.body;

  if (!['client', 'vendor', 'admin'].includes(role)) {
    res.status(400);
    throw new Error('Invalid role specified');
  }

  const user = await User.findById(userId);
  if (!user) {
    res.status(404);
    throw new Error('User not found');
  }

  user.role = role;
  await user.save();

  res.json({ message: 'User role updated successfully', user: { _id: user._id, name: user.name, email: user.email, role: user.role } });
});

// @desc    Get admin analytics data
// @route   GET /api/admin/analytics/*
// @access  Private (Admin only)
export const getAdminAnalytics = asyncHandler(async (req, res) => {
  const path = req.path;

  if (path.includes('users/growth')) {
    // User growth analytics
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const userGrowth = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { "_id.date": 1 }
      }
    ]);

    res.json(userGrowth);
  } else if (path.includes('revenue')) {
    // Revenue analytics (mock data for now)
    const revenueData = {
      totalRevenue: 125000,
      monthlyRevenue: 15000,
      growth: 12.5,
      transactions: 45
    };

    res.json(revenueData);
  } else {
    res.status(404);
    throw new Error('Analytics endpoint not found');
  }
});

// @desc    Get system features
// @route   GET /api/admin/features
// @access  Private (Admin only)
export const getSystemFeatures = asyncHandler(async (req, res) => {
  // Mock system features data
  const features = {
    realTimeMessaging: true,
    advancedAnalytics: true,
    documentVersioning: false,
    aiRecommendations: true,
    multiLanguageSupport: false
  };

  res.json(features);
});

// @desc    Update system features
// @route   PUT /api/admin/features
// @access  Private (Admin only)
export const updateSystemFeatures = asyncHandler(async (req, res) => {
  // In a real implementation, this would update system configuration
  const updatedFeatures = req.body;
  
  res.json({ message: 'System features updated successfully', features: updatedFeatures });
});

// @desc    Get system limits
// @route   GET /api/admin/limits
// @access  Private (Admin only)
export const getSystemLimits = asyncHandler(async (req, res) => {
  // Mock system limits data
  const limits = {
    maxProjectsPerUser: 50,
    maxFileUploadSize: 10485760, // 10MB
    maxBidsPerProject: 100,
    sessionTimeout: 3600000 // 1 hour
  };

  res.json(limits);
});

// @desc    Update system limits
// @route   PUT /api/admin/limits
// @access  Private (Admin only)
export const updateSystemLimits = asyncHandler(async (req, res) => {
  // In a real implementation, this would update system configuration
  const updatedLimits = req.body;
  
  res.json({ message: 'System limits updated successfully', limits: updatedLimits });
});

// @desc    Get user reports
// @route   GET /api/admin/reports/users
// @access  Private (Admin only)
export const getUserReports = asyncHandler(async (req, res) => {
  // Mock user reports data
  const reports = [
    {
      id: '1',
      reportedUser: 'John Doe',
      reportedBy: 'Jane Smith',
      reason: 'Inappropriate behavior',
      status: 'pending',
      createdAt: new Date()
    }
  ];

  res.json(reports);
});

// @desc    Scan content for moderation
// @route   POST /api/admin/moderation/scan
// @access  Private (Admin only)
export const scanContent = asyncHandler(async (req, res) => {
  const { type } = req.body;
  
  // Mock content scanning
  const scanResults = {
    scanned: 150,
    flagged: 3,
    removed: 1,
    type: type || 'all'
  };

  res.json({ message: 'Content scan completed', results: scanResults });
});

// @desc    Get system status
// @route   GET /api/admin/system/status
// @access  Private (Admin only)
export const getSystemStatus = asyncHandler(async (req, res) => {
  // Mock system status data
  const status = {
    uptime: '99.9%',
    responseTime: '120ms',
    activeUsers: 1250,
    systemLoad: '45%',
    databaseStatus: 'healthy',
    lastBackup: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours ago
  };

  res.json(status);
});

// @desc    Export data
// @route   POST /api/admin/data/export
// @access  Private (Admin only)
export const exportData = asyncHandler(async (req, res) => {
  const { type, format } = req.body;
  
  // Mock data export
  const exportResult = {
    exportId: `export_${Date.now()}`,
    type: type || 'users',
    format: format || 'json',
    status: 'initiated',
    estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000) // 5 minutes
  };

  res.json(exportResult);
});

// @desc    Get backup status
// @route   GET /api/admin/data/backups
// @access  Private (Admin only)
export const getBackupStatus = asyncHandler(async (req, res) => {
  // Mock backup data
  const backups = [
    {
      id: '1',
      date: new Date(Date.now() - 24 * 60 * 60 * 1000),
      size: '2.5GB',
      status: 'completed'
    },
    {
      id: '2',
      date: new Date(Date.now() - 48 * 60 * 60 * 1000),
      size: '2.4GB',
      status: 'completed'
    }
  ];

  res.json(backups);
});

// @desc    Cleanup data
// @route   POST /api/admin/data/cleanup
// @access  Private (Admin only)
export const cleanupData = asyncHandler(async (req, res) => {
  const { type, olderThan } = req.body;
  
  // Mock data cleanup
  const cleanupResult = {
    type: type || 'old_logs',
    olderThan: olderThan || '30d',
    itemsRemoved: 1250,
    spaceFreed: '150MB'
  };

  res.json({ message: 'Data cleanup completed', results: cleanupResult });
});

// @desc    Get database statistics
// @route   GET /api/admin/data/statistics
// @access  Private (Admin only)
export const getDatabaseStats = asyncHandler(async (req, res) => {
  // Get real database statistics
  const userCount = await User.countDocuments();
  const projectCount = await Project.countDocuments();
  const bidCount = await Bid.countDocuments();
  const messageCount = await Message.countDocuments();
  const documentCount = await Document.countDocuments();

  const stats = {
    collections: {
      users: userCount,
      projects: projectCount,
      bids: bidCount,
      messages: messageCount,
      documents: documentCount
    },
    totalSize: '1.2GB',
    indexSize: '45MB',
    lastOptimized: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
  };

  res.json(stats);
});
