import express from 'express';
import {
  createBid,
  getBids,
  getProjectBids,
  getBidById,
  updateBidStatus,
  updateBid,
  withdrawBid,
  createCounterOffer
} from '../controllers/bidController.js';
import { authMiddleware, vendorMiddleware } from '../middleware/authMiddleware.js';

const router = express.Router();

// All routes require authentication
router.use(authMiddleware);

// Get all bids (for admin or user's own bids)
router.get('/', getBids);

// Get bids for a project
router.get('/project/:projectId', getProjectBids);

// Get bid by ID
router.get('/:id', getBidById);

// Create bid (only vendors can create bids)
router.post('/', vendorMiddleware, createBid);

// Update bid status (accept/reject)
router.put('/:id/status', updateBidStatus);

// Update a bid (only bid owner)
router.put('/:id', updateBid);

// Withdraw a bid (only bid owner)
router.put('/:id/withdraw', withdrawBid);

// Create counter offer (only project owner)
router.post('/:id/counter', createCounterOffer);

export default router; 