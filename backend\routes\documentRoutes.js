import express from 'express';
import {
  uploadDocument,
  getDocuments,
  getProjectDocuments,
  getDocumentById,
  extractDocumentData,
  updateDocumentPermissions,
  deleteDocument,
  updateDocumentVersion,
  upload
} from '../controllers/documentController.js';
import { authMiddleware } from '../middleware/authMiddleware.js';

const router = express.Router();

// All routes require authentication
router.use(authMiddleware);

// Get all documents for the current user
router.get('/', getDocuments);

// Upload a new document
router.post('/', upload.single('file'), uploadDocument);

// Get documents for a project
router.get('/project/:projectId', getProjectDocuments);

// Get document by ID
router.get('/:id', getDocumentById);

// Extract embedded data from a secure document
router.post('/:id/extract', extractDocumentData);

// Update document permissions
router.put('/:id/permissions', updateDocumentPermissions);

// Delete document (soft delete)
router.delete('/:id', deleteDocument);

// Update document version
router.put('/:id/version', upload.single('file'), updateDocumentVersion);

export default router; 