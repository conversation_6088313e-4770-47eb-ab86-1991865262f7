import Analytics from '../models/analyticsModel.js';
import Project from '../models/projectModel.js';
import Bid from '../models/bidModel.js';
import User from '../models/userModel.js';
import asyncHandler from '../utils/asyncHandler.js';

// @desc    Track an analytics event
// @route   POST /api/analytics/track
// @access  Private
export const trackEvent = asyncHandler(async (req, res) => {
  const { type, projectId, metadata } = req.body;
  
  if (!type) {
    res.status(400);
    throw new Error('Event type is required');
  }
  
  // Create analytics event
  const analyticsEvent = await Analytics.create({
    user: req.user._id,
    project: projectId || null,
    type,
    metadata: metadata || {},
    ip: req.ip,
    userAgent: req.headers['user-agent']
  });
  
  res.status(201).json(analyticsEvent);
});

// @desc    Get project analytics 
// @route   GET /api/analytics/project/:projectId
// @access  Private (Project owner or Admin)
export const getProjectAnalytics = asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const { startDate, endDate } = req.query;
  
  // Check if project exists
  const project = await Project.findById(projectId);
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }
  
  // Check authorization
  const isProjectOwner = project.client.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';
  
  if (!isProjectOwner && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to view analytics for this project');
  }
  
  // Build date filter
  const dateFilter = {};
  if (startDate) {
    dateFilter.createdAt = { $gte: new Date(startDate) };
  }
  if (endDate) {
    dateFilter.createdAt = { ...dateFilter.createdAt, $lte: new Date(endDate) };
  }
  
  // Get project view analytics
  const viewAnalytics = await Analytics.find({
    project: projectId,
    type: 'project_view',
    ...dateFilter
  }).sort({ createdAt: 1 });
  
  // Get bid analytics
  const bidAnalytics = await Analytics.find({
    project: projectId,
    type: { $in: ['bid_submit', 'bid_accept', 'bid_reject'] },
    ...dateFilter
  }).sort({ createdAt: 1 });
  
  // Get document analytics
  const documentAnalytics = await Analytics.find({
    project: projectId,
    type: { $in: ['document_upload', 'document_view'] },
    ...dateFilter
  }).sort({ createdAt: 1 });
  
  // Get message analytics
  const messageAnalytics = await Analytics.find({
    project: projectId,
    type: 'message_send',
    ...dateFilter
  }).sort({ createdAt: 1 });
  
  // Compute bids over time and bid distribution
  const bids = await Bid.find({ project: projectId })
    .populate('vendor', 'name');
  
  const bidAmounts = bids.map(bid => bid.amount);
  const avgBidAmount = bidAmounts.length > 0 
    ? bidAmounts.reduce((sum, amount) => sum + amount, 0) / bidAmounts.length 
    : 0;
  
  // Group bids by date
  const bidsByDate = {};
  bids.forEach(bid => {
    const date = bid.createdAt.toISOString().split('T')[0];
    if (!bidsByDate[date]) bidsByDate[date] = 0;
    bidsByDate[date]++;
  });
  
  // Create chart data for bids over time
  const bidsOverTime = Object.keys(bidsByDate).map(date => ({
    date,
    count: bidsByDate[date]
  })).sort((a, b) => new Date(a.date) - new Date(b.date));
  
  // Create bid distribution data
  const bidDistribution = {};
  bids.forEach(bid => {
    const roundedAmount = Math.round(bid.amount / 100) * 100; // Round to nearest 100
    if (!bidDistribution[roundedAmount]) bidDistribution[roundedAmount] = 0;
    bidDistribution[roundedAmount]++;
  });
  
  const bidDistributionData = Object.keys(bidDistribution).map(amount => ({
    amount: parseInt(amount),
    count: bidDistribution[amount]
  })).sort((a, b) => a.amount - b.amount);
  
  res.json({
    projectViews: {
      total: viewAnalytics.length,
      byDate: groupByDate(viewAnalytics)
    },
    bids: {
      total: bids.length,
      average: avgBidAmount,
      overTime: bidsOverTime,
      distribution: bidDistributionData,
      status: {
        pending: bids.filter(b => b.status === 'pending').length,
        accepted: bids.filter(b => b.status === 'accepted').length,
        rejected: bids.filter(b => b.status === 'rejected').length,
        withdrawn: bids.filter(b => b.status === 'withdrawn').length
      }
    },
    documents: {
      total: documentAnalytics.filter(a => a.type === 'document_upload').length,
      views: documentAnalytics.filter(a => a.type === 'document_view').length
    },
    messages: {
      total: messageAnalytics.length,
      byDate: groupByDate(messageAnalytics)
    }
  });
});

// @desc    Get user analytics (for a client or vendor)
// @route   GET /api/analytics/user
// @access  Private
export const getUserAnalytics = asyncHandler(async (req, res) => {
  const { startDate, endDate, userId: queryUserId } = req.query;
  const { userId: pathUserId } = req.params;

  // Determine which user's analytics to fetch
  // Priority: path parameter > query parameter > current user
  const targetUserId = pathUserId ||
    (queryUserId && req.user.role === 'admin' ? queryUserId : req.user._id);
  
  // Build date filter
  const dateFilter = {};
  if (startDate) {
    dateFilter.createdAt = { $gte: new Date(startDate) };
  }
  if (endDate) {
    dateFilter.createdAt = { ...dateFilter.createdAt, $lte: new Date(endDate) };
  }
  
  // Get user
  const user = await User.findById(targetUserId);
  if (!user) {
    res.status(404);
    throw new Error('User not found');
  }
  
  // Different analytics based on user role
  if (user.role === 'client') {
    // For clients, get project creation and bid analytics
    
    // Get projects created by the client
    const projects = await Project.find({ 
      client: targetUserId,
      ...dateFilter
    });
    
    const projectIds = projects.map(p => p._id);
    
    // Get bids for the client's projects
    const bids = await Bid.find({ 
      project: { $in: projectIds } 
    })
      .populate('project', 'title budget');
    
    // Group projects by date created
    const projectsByDate = {};
    projects.forEach(project => {
      const date = project.createdAt.toISOString().split('T')[0];
      if (!projectsByDate[date]) projectsByDate[date] = 0;
      projectsByDate[date]++;
    });
    
    // Create chart data for projects over time
    const projectsOverTime = Object.keys(projectsByDate).map(date => ({
      date,
      count: projectsByDate[date]
    })).sort((a, b) => new Date(a.date) - new Date(b.date));
    
    // Calculate bid statistics
    const bidStats = {
      total: bids.length,
      averageBidsPerProject: projectIds.length > 0 ? bids.length / projectIds.length : 0,
      projectsWithNoBids: projectIds.length - new Set(bids.map(b => b.project._id.toString())).size,
      bidStatusCounts: {
        pending: bids.filter(b => b.status === 'pending').length,
        accepted: bids.filter(b => b.status === 'accepted').length,
        rejected: bids.filter(b => b.status === 'rejected').length,
        withdrawn: bids.filter(b => b.status === 'withdrawn').length
      }
    };
    
    // Calculate project stats
    const projectStats = {
      total: projects.length,
      status: {
        open: projects.filter(p => p.status === 'open').length,
        inProgress: projects.filter(p => p.status === 'in-progress').length,
        review: projects.filter(p => p.status === 'review').length,
        completed: projects.filter(p => p.status === 'completed').length,
        cancelled: projects.filter(p => p.status === 'cancelled').length
      },
      categories: projects.reduce((acc, project) => {
        acc[project.category] = (acc[project.category] || 0) + 1;
        return acc;
      }, {})
    };
    
    res.json({
      user: {
        _id: user._id,
        name: user.name,
        role: user.role
      },
      projects: {
        stats: projectStats,
        overTime: projectsOverTime
      },
      bids: bidStats
    });
    
  } else if (user.role === 'vendor') {
    // For vendors, get bid analytics
    
    // Get bids submitted by the vendor
    const bids = await Bid.find({ 
      vendor: targetUserId,
      ...dateFilter
    })
      .populate('project', 'title budget status');
    
    // Get projects the vendor is assigned to
    const assignedProjects = await Project.find({
      assignedVendor: targetUserId,
      ...dateFilter
    });
    
    // Group bids by date submitted
    const bidsByDate = {};
    bids.forEach(bid => {
      const date = bid.createdAt.toISOString().split('T')[0];
      if (!bidsByDate[date]) bidsByDate[date] = 0;
      bidsByDate[date]++;
    });
    
    // Create chart data for bids over time
    const bidsOverTime = Object.keys(bidsByDate).map(date => ({
      date,
      count: bidsByDate[date]
    })).sort((a, b) => new Date(a.date) - new Date(b.date));
    
    // Calculate bid success rate
    const acceptedBids = bids.filter(b => b.status === 'accepted');
    const successRate = bids.length > 0 
      ? (acceptedBids.length / bids.length) * 100 
      : 0;
    
    // Calculate average bid amounts by category
    const bidsByCategory = {};
    bids.forEach(bid => {
      const category = bid.project?.category || 'unknown';
      if (!bidsByCategory[category]) {
        bidsByCategory[category] = {
          total: 0,
          count: 0,
          amount: 0
        };
      }
      bidsByCategory[category].total += bid.amount;
      bidsByCategory[category].count++;
    });
    
    for (const category in bidsByCategory) {
      bidsByCategory[category].amount = bidsByCategory[category].total / bidsByCategory[category].count;
    }
    
    res.json({
      user: {
        _id: user._id,
        name: user.name,
        role: user.role
      },
      bids: {
        total: bids.length,
        accepted: acceptedBids.length,
        rejected: bids.filter(b => b.status === 'rejected').length,
        pending: bids.filter(b => b.status === 'pending').length,
        withdrawn: bids.filter(b => b.status === 'withdrawn').length,
        successRate: successRate,
        overTime: bidsOverTime,
        byCategory: bidsByCategory
      },
      assignedProjects: {
        total: assignedProjects.length,
        inProgress: assignedProjects.filter(p => p.status === 'in-progress').length,
        completed: assignedProjects.filter(p => p.status === 'completed').length
      }
    });
  } else {
    // Admin analytics would be more comprehensive
    res.status(500);
    throw new Error('Admin analytics not implemented in this version');
  }
});

// @desc    Get platform-wide analytics
// @route   GET /api/analytics/platform
// @access  Private (Admin)
export const getPlatformAnalytics = asyncHandler(async (req, res) => {
  // Check if user is admin
  if (req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to access platform analytics');
  }
  
  const { startDate, endDate } = req.query;
  
  // Build date filter
  const dateFilter = {};
  if (startDate) {
    dateFilter.createdAt = { $gte: new Date(startDate) };
  }
  if (endDate) {
    dateFilter.createdAt = { ...dateFilter.createdAt, $lte: new Date(endDate) };
  }
  
  // User stats
  const userCount = await User.countDocuments();
  const clientCount = await User.countDocuments({ role: 'client' });
  const vendorCount = await User.countDocuments({ role: 'vendor' });
  
  // Get new users over time
  const newUsers = await User.find(dateFilter)
    .select('createdAt role')
    .sort('createdAt');
  
  // Group users by date and role
  const usersByDate = {};
  newUsers.forEach(user => {
    const date = user.createdAt.toISOString().split('T')[0];
    if (!usersByDate[date]) {
      usersByDate[date] = {
        total: 0,
        client: 0,
        vendor: 0
      };
    }
    usersByDate[date].total++;
    usersByDate[date][user.role]++;
  });
  
  // Create chart data for users over time
  const usersOverTime = Object.keys(usersByDate).map(date => ({
    date,
    ...usersByDate[date]
  })).sort((a, b) => new Date(a.date) - new Date(b.date));
  
  // Project stats
  const projectCount = await Project.countDocuments();
  const openProjectCount = await Project.countDocuments({ status: 'open' });
  const inProgressProjectCount = await Project.countDocuments({ status: 'in-progress' });
  const completedProjectCount = await Project.countDocuments({ status: 'completed' });
  
  // Get projects over time
  const projects = await Project.find(dateFilter)
    .select('createdAt category status budget')
    .sort('createdAt');
  
  // Group projects by date
  const projectsByDate = {};
  projects.forEach(project => {
    const date = project.createdAt.toISOString().split('T')[0];
    if (!projectsByDate[date]) projectsByDate[date] = 0;
    projectsByDate[date]++;
  });
  
  // Create chart data for projects over time
  const projectsOverTime = Object.keys(projectsByDate).map(date => ({
    date,
    count: projectsByDate[date]
  })).sort((a, b) => new Date(a.date) - new Date(b.date));
  
  // Project categories
  const projectCategories = projects.reduce((acc, project) => {
    acc[project.category] = (acc[project.category] || 0) + 1;
    return acc;
  }, {});
  
  // Get average budget by category
  const budgetByCategory = projects.reduce((acc, project) => {
    if (!acc[project.category]) {
      acc[project.category] = {
        total: 0,
        count: 0
      };
    }
    acc[project.category].total += project.budget;
    acc[project.category].count++;
    return acc;
  }, {});
  
  Object.keys(budgetByCategory).forEach(category => {
    budgetByCategory[category].average = budgetByCategory[category].total / budgetByCategory[category].count;
  });
  
  // Bid stats
  const bidCount = await Bid.countDocuments();
  const acceptedBidCount = await Bid.countDocuments({ status: 'accepted' });
  
  // Calculate platform metrics
  const avgBidsPerProject = projectCount > 0 
    ? bidCount / projectCount 
    : 0;
  const projectCompletionRate = (projectCount - openProjectCount - inProgressProjectCount) > 0 
    ? (completedProjectCount / (projectCount - openProjectCount - inProgressProjectCount)) * 100 
    : 0;
  
  res.json({
    users: {
      total: userCount,
      clients: clientCount,
      vendors: vendorCount,
      overTime: usersOverTime
    },
    projects: {
      total: projectCount,
      open: openProjectCount,
      inProgress: inProgressProjectCount,
      completed: completedProjectCount,
      overTime: projectsOverTime,
      categories: projectCategories,
      budgetByCategory: budgetByCategory,
      completionRate: projectCompletionRate
    },
    bids: {
      total: bidCount,
      accepted: acceptedBidCount,
      avgPerProject: avgBidsPerProject,
      acceptanceRate: bidCount > 0 ? (acceptedBidCount / bidCount) * 100 : 0
    }
  });
});

// Helper function to group analytics events by date
const groupByDate = (events) => {
  const groupedData = {};
  
  events.forEach(event => {
    const date = event.createdAt.toISOString().split('T')[0];
    if (!groupedData[date]) groupedData[date] = 0;
    groupedData[date]++;
  });
  
  return Object.keys(groupedData).map(date => ({
    date,
    count: groupedData[date]
  })).sort((a, b) => new Date(a.date) - new Date(b.date));
};

// @desc    Get dashboard statistics
// @route   GET /api/analytics/dashboard
// @access  Private
export const getDashboardStats = asyncHandler(async (req, res) => {
  const userId = req.user._id;
  const userRole = req.user.role;

  let stats = {};

  if (userRole === 'client') {
    // Client dashboard stats
    const totalProjects = await Project.countDocuments({ client: userId });
    const activeProjects = await Project.countDocuments({
      client: userId,
      status: { $in: ['open', 'in-progress'] }
    });
    const completedProjects = await Project.countDocuments({
      client: userId,
      status: 'completed'
    });

    // Get total bids received
    const projects = await Project.find({ client: userId }).select('_id');
    const projectIds = projects.map(p => p._id);
    const totalBids = await Bid.countDocuments({ project: { $in: projectIds } });

    stats = {
      totalProjects,
      activeProjects,
      completedProjects,
      totalBids,
      role: 'client'
    };
  } else if (userRole === 'vendor') {
    // Vendor dashboard stats
    const totalBids = await Bid.countDocuments({ vendor: userId });
    const acceptedBids = await Bid.countDocuments({
      vendor: userId,
      status: 'accepted'
    });
    const pendingBids = await Bid.countDocuments({
      vendor: userId,
      status: 'pending'
    });
    const activeProjects = await Project.countDocuments({
      assignedVendor: userId,
      status: { $in: ['in-progress', 'review'] }
    });

    stats = {
      totalBids,
      acceptedBids,
      pendingBids,
      activeProjects,
      role: 'vendor'
    };
  }

  res.json(stats);
});

// @desc    Get revenue analytics
// @route   GET /api/analytics/revenue
// @access  Private (Admin for all data, clients/vendors for their own data)
export const getRevenueAnalytics = asyncHandler(async (req, res) => {
  const { startDate, endDate } = req.query;
  const userId = req.user._id;
  const userRole = req.user.role;

  // Build date filter
  const dateFilter = {};
  if (startDate || endDate) {
    dateFilter.createdAt = {};
    if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
    if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
  }

  // Role-based filtering
  let projectFilter = { status: 'completed', ...dateFilter };
  if (userRole === 'client') {
    projectFilter.client = userId;
  } else if (userRole === 'vendor') {
    projectFilter.assignedVendor = userId;
  }
  // Admin can see all data (no additional filtering)

  // Get completed projects with their budgets
  const completedProjects = await Project.find(projectFilter).populate('winningBid', 'amount');

  const totalRevenue = completedProjects.reduce((sum, project) => {
    return sum + (project.winningBid?.amount || project.budget);
  }, 0);

  const projectCount = completedProjects.length;
  const averageProjectValue = projectCount > 0 ? totalRevenue / projectCount : 0;

  // Group by month
  const monthlyRevenue = {};
  completedProjects.forEach(project => {
    const month = project.createdAt.toISOString().substring(0, 7); // YYYY-MM
    const amount = project.winningBid?.amount || project.budget;

    if (!monthlyRevenue[month]) monthlyRevenue[month] = 0;
    monthlyRevenue[month] += amount;
  });

  res.json({
    totalRevenue,
    projectCount,
    averageProjectValue,
    monthlyRevenue: Object.keys(monthlyRevenue).map(month => ({
      month,
      revenue: monthlyRevenue[month]
    })).sort((a, b) => a.month.localeCompare(b.month))
  });
});

// @desc    Get user activity analytics
// @route   GET /api/analytics/user/:userId/activity or GET /api/analytics/user/activity
// @access  Private
export const getUserActivityAnalytics = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { startDate, endDate } = req.query;

  // Use current user if no userId provided
  const targetUserId = userId || req.user._id.toString();

  // Check authorization
  if (req.user._id.toString() !== targetUserId && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to view this user\'s analytics');
  }

  // Build date filter
  const dateFilter = { user: targetUserId };
  if (startDate || endDate) {
    dateFilter.createdAt = {};
    if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
    if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
  }

  const activities = await Analytics.find(dateFilter).sort({ createdAt: -1 });

  // Group by activity type
  const activityByType = {};
  activities.forEach(activity => {
    if (!activityByType[activity.type]) activityByType[activity.type] = 0;
    activityByType[activity.type]++;
  });

  // Group by date
  const dailyActivity = {};
  activities.forEach(activity => {
    const date = activity.createdAt.toISOString().split('T')[0];
    if (!dailyActivity[date]) dailyActivity[date] = 0;
    dailyActivity[date]++;
  });

  res.json({
    totalActivities: activities.length,
    activityByType,
    dailyActivity: Object.keys(dailyActivity).map(date => ({
      date,
      count: dailyActivity[date]
    })).sort((a, b) => new Date(a.date) - new Date(b.date)),
    recentActivities: activities.slice(0, 10)
  });
});

// @desc    Get project performance analytics
// @route   GET /api/analytics/project/:projectId/performance
// @access  Private
export const getProjectPerformanceAnalytics = asyncHandler(async (req, res) => {
  const { projectId } = req.params;

  const project = await Project.findById(projectId)
    .populate('client', 'name')
    .populate('assignedVendor', 'name')
    .populate('bids');

  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }

  // Check authorization
  const isProjectOwner = project.client._id.toString() === req.user._id.toString();
  const isAssignedVendor = project.assignedVendor &&
    project.assignedVendor._id.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';

  if (!isProjectOwner && !isAssignedVendor && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to view this project\'s analytics');
  }

  // Get project analytics
  const projectAnalytics = await Analytics.find({ project: projectId });

  // Calculate metrics
  const totalViews = projectAnalytics.filter(a => a.type === 'project_view').length;
  const totalBids = await Bid.countDocuments({ project: projectId });
  const averageBidAmount = totalBids > 0 ?
    (await Bid.aggregate([
      { $match: { project: project._id } },
      { $group: { _id: null, avgAmount: { $avg: '$amount' } } }
    ]))[0]?.avgAmount || 0 : 0;

  // Time to completion
  const timeToCompletion = project.status === 'completed' ?
    Math.ceil((project.updatedAt - project.createdAt) / (1000 * 60 * 60 * 24)) : null;

  res.json({
    project: {
      title: project.title,
      status: project.status,
      budget: project.budget,
      createdAt: project.createdAt,
      deadline: project.deadline
    },
    metrics: {
      totalViews,
      totalBids,
      averageBidAmount,
      timeToCompletion
    },
    analytics: projectAnalytics
  });
});

// @desc    Get projects analytics overview
// @route   GET /api/analytics/projects
// @access  Private
export const getProjectsAnalytics = asyncHandler(async (req, res) => {
  const { category, status } = req.query;

  // Build filter
  const filter = {};
  if (category) filter.category = category;
  if (status) filter.status = status;

  // Get projects with analytics
  const projects = await Project.find(filter)
    .populate('client', 'name')
    .populate('assignedVendor', 'name')
    .sort({ createdAt: -1 });

  // Calculate analytics
  const totalProjects = projects.length;
  const projectsByStatus = projects.reduce((acc, project) => {
    acc[project.status] = (acc[project.status] || 0) + 1;
    return acc;
  }, {});

  const projectsByCategory = projects.reduce((acc, project) => {
    acc[project.category] = (acc[project.category] || 0) + 1;
    return acc;
  }, {});

  res.json({
    total: totalProjects,
    byStatus: projectsByStatus,
    byCategory: projectsByCategory,
    projects: projects.slice(0, 10) // Return first 10 for preview
  });
});

// @desc    Get system performance analytics
// @route   GET /api/analytics/system/performance
// @access  Private
export const getSystemPerformanceAnalytics = asyncHandler(async (req, res) => {
  // Mock system performance data
  const performanceData = {
    responseTime: {
      average: 120,
      p95: 250,
      p99: 500
    },
    throughput: {
      requestsPerSecond: 45,
      peakRps: 120
    },
    errorRate: 0.02,
    uptime: 99.9,
    memoryUsage: 65,
    cpuUsage: 45
  };

  res.json(performanceData);
});

// @desc    Get user performance analytics
// @route   GET /api/analytics/user/performance
// @access  Private
export const getUserPerformanceAnalytics = asyncHandler(async (req, res) => {
  const userId = req.user._id;

  // Get user's activity metrics
  const userAnalytics = await Analytics.find({ user: userId })
    .sort({ createdAt: -1 })
    .limit(100);

  // Calculate performance metrics
  const activityByType = userAnalytics.reduce((acc, activity) => {
    acc[activity.type] = (acc[activity.type] || 0) + 1;
    return acc;
  }, {});

  const dailyActivity = userAnalytics.reduce((acc, activity) => {
    const date = activity.createdAt.toISOString().split('T')[0];
    acc[date] = (acc[date] || 0) + 1;
    return acc;
  }, {});

  res.json({
    totalActivities: userAnalytics.length,
    activityByType,
    dailyActivity: Object.keys(dailyActivity).map(date => ({
      date,
      count: dailyActivity[date]
    })).sort((a, b) => new Date(a.date) - new Date(b.date)),
    averageActivitiesPerDay: Object.keys(dailyActivity).length > 0
      ? userAnalytics.length / Object.keys(dailyActivity).length
      : 0
  });
});