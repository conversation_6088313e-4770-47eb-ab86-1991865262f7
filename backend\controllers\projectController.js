import Project from '../models/projectModel.js';
import Bid from '../models/bidModel.js';
import User from '../models/userModel.js';
import asyncHandler from '../utils/asyncHandler.js';
import { calculateOptimalPrice } from '../utils/priceOptimization.js';

// @desc    Create a new project
// @route   POST /api/projects
// @access  Private (Client)
export const createProject = asyncHandler(async (req, res) => {
  const {
    title,
    description,
    budget,
    deadline,
    timeline,
    category,
    skills,
    requirements,
    attachments,
    visibility
  } = req.body;

  // Handle backward compatibility for timeline/deadline and skills/requirements
  const projectDeadline = deadline || (timeline ? new Date(Date.now() + 90 * 24 * 60 * 60 * 1000) : null); // Default 3 months if timeline provided
  const projectSkills = skills || requirements || [];

  // Validate required fields
  if (!title || !description || !budget || !projectDeadline || !category || !projectSkills || projectSkills.length === 0) {
    res.status(400);
    throw new Error('Please fill in all required fields');
  }

  // Ensure user is a client
  if (req.user.role !== 'client' && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Only clients can create projects');
  }

  // Create new project
  const project = await Project.create({
    title,
    description,
    client: req.user._id,
    budget,
    deadline: projectDeadline,
    category,
    skills: projectSkills,
    attachments: attachments || [],
    visibility: visibility || 'public'
  });

  // Generate ML price recommendation
  try {
    // Get historical projects for price optimization
    const historicalProjects = await Project.find({
      status: { $in: ['completed', 'in-progress'] },
      category
    })
      .populate({
        path: 'winningBid',
        model: 'Bid',
        select: 'amount'
      })
      .populate({
        path: 'bids',
        model: 'Bid',
        select: 'amount status'
      });

    const priceRecommendation = await calculateOptimalPrice(project, historicalProjects);

    // Update project with ML recommendation
    project.mlPriceRecommendation = priceRecommendation.recommendedPrice;
    await project.save();

  } catch (error) {
    console.error('Price optimization error:', error);
    // Don't fail project creation if ML fails
  }

  res.status(201).json(project);
});

// @desc    Get all projects with filtering and pagination
// @route   GET /api/projects
// @access  Public (with optional authentication for personalized results)
export const getProjects = asyncHandler(async (req, res) => {
  const pageSize = parseInt(req.query.limit) || 10;
  const page = parseInt(req.query.page) || 1;

  // Build filter object
  const filter = {};

  // Filter by status
  if (req.query.status) {
    filter.status = req.query.status;
  }

  // Filter by category
  if (req.query.category) {
    filter.category = req.query.category;
  }

  // Filter by skills
  if (req.query.skills) {
    filter.skills = { $in: req.query.skills.split(',') };
  }

  // Filter by budget range
  if (req.query.minBudget || req.query.maxBudget) {
    filter.budget = {};
    if (req.query.minBudget) {
      filter.budget.$gte = parseInt(req.query.minBudget);
    }
    if (req.query.maxBudget) {
      filter.budget.$lte = parseInt(req.query.maxBudget);
    }
  }

  // Apply visibility filters based on authentication status
  if (req.user) {
    // User is authenticated - apply role-based filtering
    if (req.user.role !== 'admin') {
      if (req.user.role === 'client') {
        // Clients can see their own projects and public projects
        filter.$or = [
          { client: req.user._id },
          { visibility: 'public' }
        ];
      } else if (req.user.role === 'vendor') {
        // Vendors can see public projects, projects assigned to them, and projects they've bid on
        const vendorBids = await Bid.find({ vendor: req.user._id }).select('project');
        const projectIds = vendorBids.map(bid => bid.project);

        filter.$or = [
          { assignedVendor: req.user._id },
          { visibility: 'public' },
          { _id: { $in: projectIds } }
        ];
      }
    }
  } else {
    // User is not authenticated - only show public projects
    filter.visibility = 'public';
  }

  const count = await Project.countDocuments(filter);

  const projects = await Project.find(filter)
    .populate('client', 'name email company companyLogo')
    .populate('assignedVendor', 'name email company companyLogo')
    .sort({ createdAt: -1 })
    .skip(pageSize * (page - 1))
    .limit(pageSize);

  // If no pagination parameters are provided, return just the array for backward compatibility
  if (!req.query.page && !req.query.limit && !req.query.paginate) {
    res.json(projects);
  } else {
    res.json({
      projects,
      page,
      pages: Math.ceil(count / pageSize),
      total: count
    });
  }
});

// @desc    Get project by ID
// @route   GET /api/projects/:id
// @access  Public (with optional authentication for personalized results)
export const getProjectById = asyncHandler(async (req, res) => {
  const project = await Project.findById(req.params.id)
    .populate('client', 'name email company companyLogo')
    .populate('assignedVendor', 'name email company companyLogo')
    .populate({
      path: 'winningBid',
      populate: {
        path: 'vendor',
        select: 'name email company companyLogo'
      }
    })
    .populate({
      path: 'bids',
      populate: {
        path: 'vendor',
        select: 'name email company companyLogo rating'
      }
    })
    .populate('documents');

  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }

  // Check visibility permissions based on authentication status
  if (req.user) {
    // User is authenticated - apply role-based visibility checks
    if (project.visibility === 'private' &&
        project.client.toString() !== req.user._id.toString() &&
        req.user.role !== 'admin') {
      res.status(403);
      throw new Error('Not authorized to view this project');
    }

    // For invite-only projects, check if user is invited (has bid on it) or is the client
    if (project.visibility === 'invite-only' &&
        project.client.toString() !== req.user._id.toString() &&
        req.user.role !== 'admin') {

      const hasBid = await Bid.exists({
        project: project._id,
        vendor: req.user._id
      });

      if (!hasBid && project.assignedVendor?.toString() !== req.user._id.toString()) {
        res.status(403);
        throw new Error('Not authorized to view this invite-only project');
      }
    }
  } else {
    // User is not authenticated - only allow public projects
    if (project.visibility !== 'public') {
      res.status(403);
      throw new Error('Authentication required to view this project');
    }
  }
  
  res.json(project);
});

// @desc    Update a project
// @route   PUT /api/projects/:id
// @access  Private (Client and Admin)
export const updateProject = asyncHandler(async (req, res) => {
  const project = await Project.findById(req.params.id);
  
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }
  
  // Check ownership
  if (project.client.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to update this project');
  }
  
  // Only allow updates if the project is not in progress or completed
  if (project.status !== 'open' && req.user.role !== 'admin') {
    res.status(400);
    throw new Error('Cannot update a project that is already in progress or completed');
  }
  
  const {
    title,
    description,
    budget,
    deadline,
    category,
    skills,
    attachments,
    visibility,
    status
  } = req.body;
  
  // Update fields
  project.title = title || project.title;
  project.description = description || project.description;
  project.budget = budget || project.budget;
  project.deadline = deadline || project.deadline;
  project.category = category || project.category;
  project.skills = skills || project.skills;
  project.attachments = attachments || project.attachments;
  project.visibility = visibility || project.visibility;
  
  // Only admin can update status
  if (status && req.user.role === 'admin') {
    project.status = status;
  }
  
  const updatedProject = await project.save();
  
  res.json(updatedProject);
});

// @desc    Delete a project
// @route   DELETE /api/projects/:id
// @access  Private (Client and Admin)
export const deleteProject = asyncHandler(async (req, res) => {
  const project = await Project.findById(req.params.id);
  
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }
  
  // Check ownership
  if (project.client.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    res.status(403);
    throw new Error('Not authorized to delete this project');
  }
  
  // Only allow deletion if no bids are accepted
  if (project.status !== 'open' && req.user.role !== 'admin') {
    res.status(400);
    throw new Error('Cannot delete a project that is already in progress or completed');
  }
  
  await project.remove();
  
  res.json({ message: 'Project removed' });
}); 