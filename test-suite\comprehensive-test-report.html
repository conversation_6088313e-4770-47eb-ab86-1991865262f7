<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GlobalConnect Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #2c3e50; }
        .metric-label { color: #7f8c8d; margin-top: 5px; }
        .suite { margin-bottom: 30px; border: 1px solid #e1e8ed; border-radius: 6px; overflow: hidden; }
        .suite-header { background: #3498db; color: white; padding: 15px; font-weight: bold; }
        .suite-content { padding: 15px; }
        .test-item { padding: 10px; border-bottom: 1px solid #ecf0f1; }
        .test-item:last-child { border-bottom: none; }
        .passed { color: #27ae60; }
        .failed { color: #e74c3c; }
        .success-rate { font-weight: bold; }
        .success-high { color: #27ae60; }
        .success-medium { color: #f39c12; }
        .success-low { color: #e74c3c; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 GlobalConnect Comprehensive Test Report</h1>
            <p>Generated on 2025-07-21T12:27:14.196Z</p>
        </div>

        <div class="summary">
            <div class="metric">
                <div class="metric-value">48</div>
                <div class="metric-label">Total Tests</div>
            </div>
            <div class="metric">
                <div class="metric-value passed">16</div>
                <div class="metric-label">Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value failed">32</div>
                <div class="metric-label">Failed</div>
            </div>
            <div class="metric">
                <div class="metric-value success-rate success-low">33.33%</div>
                <div class="metric-label">Success Rate</div>
            </div>
        </div>

        
        <div class="suite">
            <div class="suite-header">
                📋 Project Management - 1/8 tests passed (12.50%)
            </div>
            <div class="suite-content">
                
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Browse Projects</strong> - Test browsing 8+ demo projects across different categories
                    <br><small>Duration: 147ms</small>
                    <br><small class="failed">Errors: Projects response is not an array</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Create Project</strong> - Test creating new projects with budget and timeline
                    <br><small>Duration: 50ms</small>
                    <br><small class="failed">Errors: Request failed with status code 400</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Project Details</strong> - Test viewing detailed project information
                    <br><small>Duration: 132ms</small>
                    <br><small class="failed">Errors: Cannot read properties of undefined (reading '_id')</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Update Project Status</strong> - Test managing project status and lifecycle
                    <br><small>Duration: 182ms</small>
                    <br><small class="failed">Errors: projects.filter is not a function</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Project Milestones</strong> - Test project milestone management
                    <br><small>Duration: 182ms</small>
                    <br><small class="failed">Errors: Cannot read properties of undefined (reading '_id')</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Project Analytics</strong> - Test project performance analytics
                    <br><small>Duration: 250ms</small>
                    <br><small class="failed">Errors: Cannot read properties of undefined (reading '_id')</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Project Categories</strong> - Test project categorization and filtering
                    <br><small>Duration: 196ms</small>
                    <br><small class="failed">Errors: allProjects.map is not a function</small>
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Project Search</strong> - Test project search functionality
                    <br><small>Duration: 434ms</small>
                    
                </div>
                
            </div>
        </div>
        
        <div class="suite">
            <div class="suite-header">
                💰 Bidding System - 0/8 tests passed (0.00%)
            </div>
            <div class="suite-content">
                
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>View Bids</strong> - Test viewing 20+ realistic bids on various projects
                    <br><small>Duration: 89ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Submit Bid</strong> - Test submitting detailed proposals
                    <br><small>Duration: 195ms</small>
                    <br><small class="failed">Errors: projects.filter is not a function</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Bid with Attachments</strong> - Test submitting proposals with file attachments
                    <br><small>Duration: 179ms</small>
                    <br><small class="failed">Errors: projects.filter is not a function</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Accept Bid</strong> - Test accepting bids as a client
                    <br><small>Duration: 129ms</small>
                    <br><small class="failed">Errors: projects.filter is not a function</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Reject Bid</strong> - Test rejecting bids as a client
                    <br><small>Duration: 131ms</small>
                    <br><small class="failed">Errors: projects.filter is not a function</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Bid Status Tracking</strong> - Test tracking bid status and changes
                    <br><small>Duration: 136ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Bid Negotiation</strong> - Test bid negotiation and communication
                    <br><small>Duration: 91ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Vendor Bid History</strong> - Test vendor bid history and analytics
                    <br><small>Duration: 95ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
            </div>
        </div>
        
        <div class="suite">
            <div class="suite-header">
                💬 Messaging System - 4/8 tests passed (50.00%)
            </div>
            <div class="suite-content">
                
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>View Messages</strong> - Test viewing existing messages and conversations
                    <br><small>Duration: 85ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Send Message</strong> - Test sending messages between users
                    <br><small>Duration: 88ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Message with Attachments</strong> - Test sending messages with file attachments
                    <br><small>Duration: 88ms</small>
                    
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Project Conversations</strong> - Test project-specific messaging
                    <br><small>Duration: 167ms</small>
                    <br><small class="failed">Errors: Cannot read properties of undefined (reading '_id')</small>
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Group Conversations</strong> - Test group messaging for team collaboration
                    <br><small>Duration: 201ms</small>
                    
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Message Delivery</strong> - Test real-time message delivery and read receipts
                    <br><small>Duration: 88ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Message Search</strong> - Test searching messages by content and metadata
                    <br><small>Duration: 411ms</small>
                    
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Conversation Management</strong> - Test conversation organization and management
                    <br><small>Duration: 698ms</small>
                    
                </div>
                
            </div>
        </div>
        
        <div class="suite">
            <div class="suite-header">
                📁 Document Management - 2/8 tests passed (25.00%)
            </div>
            <div class="suite-content">
                
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>View Documents</strong> - Test viewing 30+ demo documents across file types
                    <br><small>Duration: 126ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Upload Document</strong> - Test uploading documents with metadata
                    <br><small>Duration: 82ms</small>
                    
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Security Levels</strong> - Test document security levels and access control
                    <br><small>Duration: 83ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Version Control</strong> - Test document version control and change tracking
                    <br><small>Duration: 82ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Role-based Access</strong> - Test role-based document access permissions
                    <br><small>Duration: 208ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Document Types</strong> - Test handling of different document file types
                    <br><small>Duration: 141ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Document Search</strong> - Test document search and filtering capabilities
                    <br><small>Duration: 493ms</small>
                    
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Document Sharing</strong> - Test document sharing and collaboration features
                    <br><small>Duration: 83ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
            </div>
        </div>
        
        <div class="suite">
            <div class="suite-header">
                📊 Analytics Dashboard - 4/8 tests passed (50.00%)
            </div>
            <div class="suite-content">
                
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Dashboard Statistics</strong> - Test personalized dashboard statistics for different user types
                    <br><small>Duration: 622ms</small>
                    
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Project Analytics</strong> - Test project performance analytics and insights
                    <br><small>Duration: 83ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Revenue Tracking</strong> - Test revenue and earnings tracking functionality
                    <br><small>Duration: 82ms</small>
                    <br><small class="failed">Errors: Request failed with status code 403</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>User Engagement</strong> - Test user activity and engagement metrics
                    <br><small>Duration: 208ms</small>
                    <br><small class="failed">Errors: Request failed with status code 500</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Performance Metrics</strong> - Test system and user performance metrics
                    <br><small>Duration: 164ms</small>
                    <br><small class="failed">Errors: Request failed with status code 500</small>
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Analytics Filtering</strong> - Test filtering and date range functionality in analytics
                    <br><small>Duration: 1645ms</small>
                    
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Data Export</strong> - Test analytics data export functionality
                    <br><small>Duration: 305ms</small>
                    
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Real-time Updates</strong> - Test real-time analytics updates and live data
                    <br><small>Duration: 277ms</small>
                    
                </div>
                
            </div>
        </div>
        
        <div class="suite">
            <div class="suite-header">
                👑 Admin Panel - 5/8 tests passed (62.50%)
            </div>
            <div class="suite-content">
                
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>User Management</strong> - Test user management and administration features
                    <br><small>Duration: 3ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Role Assignment</strong> - Test user role management and permissions
                    <br><small>Duration: 3ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="failed">❌</span>
                    <strong>Platform Analytics</strong> - Test platform-wide analytics and reporting
                    <br><small>Duration: 4ms</small>
                    <br><small class="failed">Errors: Request failed with status code 404</small>
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>System Configuration</strong> - Test system settings and configuration management
                    <br><small>Duration: 23ms</small>
                    
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Content Moderation</strong> - Test content moderation and security features
                    <br><small>Duration: 24ms</small>
                    
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Security Management</strong> - Test security monitoring and management features
                    <br><small>Duration: 19ms</small>
                    
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>System Monitoring</strong> - Test system health and performance monitoring
                    <br><small>Duration: 21ms</small>
                    
                </div>
                
                <div class="test-item">
                    <span class="passed">✅</span>
                    <strong>Data Management</strong> - Test data backup, export, and maintenance features
                    <br><small>Duration: 20ms</small>
                    
                </div>
                
            </div>
        </div>
        
    </div>
</body>
</html>