import express from 'express';
import {
  getUserAnalytics,
  getProjectAnalytics,
  getProjectsAnalytics,
  getPlatformAnalytics,
  trackEvent,
  getDashboardStats,
  getRevenueAnalytics,
  getUserActivityAnalytics,
  getProjectPerformanceAnalytics,
  getSystemPerformanceAnalytics,
  getUserPerformanceAnalytics
} from '../controllers/analyticsController.js';
import { authMiddleware, adminMiddleware } from '../middleware/authMiddleware.js';

const router = express.Router();

// All routes require authentication
router.use(authMiddleware);

// Track analytics event
router.post('/track', trackEvent);

// Get dashboard statistics for current user
router.get('/dashboard', getDashboardStats);

// Get user analytics (with optional userId parameter)
router.get('/user', getUserAnalytics);

// Get user analytics by specific user ID
router.get('/user/:userId', getUserAnalytics);

// Get project analytics
router.get('/project/:projectId', getProjectAnalytics);

// Get user activity analytics
router.get('/user/:userId/activity', getUserActivityAnalytics);

// Get project performance analytics
router.get('/project/:projectId/performance', getProjectPerformanceAnalytics);

// Project analytics routes
router.get('/projects', getProjectsAnalytics);
router.get('/projects/:projectId/performance', getProjectPerformanceAnalytics);

// System performance routes
router.get('/system/performance', getSystemPerformanceAnalytics);
router.get('/user/performance', getUserPerformanceAnalytics);

// Revenue analytics (accessible to clients for their own data, admin for all data)
router.get('/revenue', getRevenueAnalytics);

// Admin only routes
router.get('/platform', adminMiddleware, getPlatformAnalytics);

export default router;
