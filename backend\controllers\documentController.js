import Document from '../models/documentModel.js';
import Project from '../models/projectModel.js';
import asyncHandler from '../utils/asyncHandler.js';
import { uploadFileToCloudinary, deleteFileFromCloudinary, getSecureUrl } from '../utils/cloudinaryUpload.js';
import * as rdhUtils from '../utils/rdhUtils.js';
import multer from 'multer';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { fileURLToPath } from 'url';
import { embedDataInDocument, extractDataFromDocument } from '../utils/rdh.js';
import os from 'os';

// Configure multer storage for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const tempDir = path.join(os.tmpdir(), 'globalconnect-uploads');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    cb(null, tempDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});

// File filter to restrict file types
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only PDF, JPEG, PNG, DOC and DOCX are allowed.'), false);
  }
};

export const upload = multer({ 
  storage, 
  fileFilter,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB limit
});

// @desc    Upload a new document with RDH
// @route   POST /api/documents
// @access  Private
export const uploadDocument = asyncHandler(async (req, res) => {
  const { projectId, title, description, securityLevel, metadata } = req.body;
  
  if (!req.file) {
    res.status(400);
    throw new Error('Please upload a file');
  }
  
  if (!projectId || !title) {
    res.status(400);
    throw new Error('Project ID and title are required');
  }
  
  // Check if project exists
  const project = await Project.findById(projectId);
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }
  
  // Check authorization
  const isProjectOwner = project.client.toString() === req.user._id.toString();
  const isAssignedVendor = project.assignedVendor && 
    project.assignedVendor.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';
  
  if (!isProjectOwner && !isAssignedVendor && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to upload documents to this project');
  }
  
  // Apply RDH to embed metadata into the document
  const documentMetadata = {
    uploadedBy: req.user._id.toString(),
    projectId,
    timestamp: new Date().toISOString(),
    userDefined: metadata ? JSON.parse(metadata) : {}
  };
  
  // Path to the uploaded file
  const filePath = req.file.path;
  let securedFilePath;
  
  try {
    // Apply RDH to embed data in the document
    securedFilePath = await embedDataInDocument(
      filePath, 
      documentMetadata, 
      parseInt(securityLevel) || 1
    );
    
    // Upload to Cloudinary
    const fileBuffer = fs.readFileSync(securedFilePath);
    const uploadResult = await uploadFileToCloudinary(
      {
        buffer: fileBuffer,
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
      },
      `documents/${projectId}`
    );
    
    // Create document record in database
    const document = await Document.create({
      title,
      description,
      project: projectId,
      fileKey: uploadResult.key,
      fileType: req.file.mimetype,
      fileName: req.file.originalname,
      fileSize: req.file.size,
      uploadedBy: req.user._id,
      securityLevel: parseInt(securityLevel) || 1,
      metadata: documentMetadata
    });
    
    // Clean up temporary files
    fs.unlinkSync(filePath);
    fs.unlinkSync(securedFilePath);
    
    res.status(201).json({
      document,
      url: uploadResult.url
    });
    
  } catch (error) {
    // Clean up files on error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    if (securedFilePath && fs.existsSync(securedFilePath)) {
      fs.unlinkSync(securedFilePath);
    }
    
    res.status(500);
    throw new Error(`Document upload failed: ${error.message}`);
  }
});

// @desc    Get all documents for the current user
// @route   GET /api/documents
// @access  Private
export const getDocuments = asyncHandler(async (req, res) => {
  const pageSize = parseInt(req.query.limit) || 20;
  const page = parseInt(req.query.page) || 1;
  const userId = req.user._id;

  // Build filter object
  const filter = { isDeleted: false };

  // Role-based filtering
  if (req.user.role === 'admin') {
    // Admin can see all documents
    // No additional filtering needed
  } else if (req.user.role === 'client') {
    // Clients can see documents from their projects
    const userProjects = await Project.find({ client: userId }).select('_id');
    const projectIds = userProjects.map(p => p._id);
    filter.project = { $in: projectIds };
  } else if (req.user.role === 'vendor') {
    // Vendors can see documents from projects they're assigned to or have bid on
    const assignedProjects = await Project.find({ assignedVendor: userId }).select('_id');
    const assignedProjectIds = assignedProjects.map(p => p._id);

    // Also include projects they've uploaded documents to
    filter.$or = [
      { project: { $in: assignedProjectIds } },
      { uploadedBy: userId }
    ];
  } else {
    res.status(403);
    throw new Error('Not authorized to view documents');
  }

  // Add search functionality
  if (req.query.search) {
    filter.$and = filter.$and || [];
    filter.$and.push({
      $or: [
        { title: { $regex: req.query.search, $options: 'i' } },
        { description: { $regex: req.query.search, $options: 'i' } },
        { fileName: { $regex: req.query.search, $options: 'i' } }
      ]
    });
  }

  // Add tag filtering
  if (req.query.tags) {
    filter.tags = { $in: req.query.tags.split(',') };
  }

  // Add date filtering
  if (req.query.since) {
    filter.createdAt = { $gte: new Date(req.query.since) };
  }

  const count = await Document.countDocuments(filter);

  const documents = await Document.find(filter)
    .populate('uploadedBy', 'name email')
    .populate('project', 'title client assignedVendor')
    .sort({ createdAt: -1 })
    .skip(pageSize * (page - 1))
    .limit(pageSize);

  res.json(documents);
});

// @desc    Get all documents for a project
// @route   GET /api/documents/project/:projectId
// @access  Private
export const getProjectDocuments = asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  
  // Check if project exists
  const project = await Project.findById(projectId);
  if (!project) {
    res.status(404);
    throw new Error('Project not found');
  }
  
  // Check authorization
  const isProjectOwner = project.client.toString() === req.user._id.toString();
  const isAssignedVendor = project.assignedVendor && 
    project.assignedVendor.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';
  
  if (!isProjectOwner && !isAssignedVendor && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to view documents for this project');
  }
  
  // Get all documents for the project
  const documents = await Document.find({ project: projectId, isDeleted: false })
    .populate('uploadedBy', 'name email')
    .sort({ createdAt: -1 });
  
  // Generate signed URLs for each document
  const documentsWithUrls = await Promise.all(documents.map(async (doc) => {
    try {
      const getCommand = new GetObjectCommand({
        Bucket: bucketName,
        Key: doc.fileKey
      });
      
      const signedUrl = await getSignedUrl(s3, getCommand, { expiresIn: 3600 });
      
      return {
        ...doc.toObject(),
        url: signedUrl
      };
    } catch (error) {
      return {
        ...doc.toObject(),
        url: null,
        error: 'Could not generate URL'
      };
    }
  }));
  
  res.json(documentsWithUrls);
});

// @desc    Get document by ID
// @route   GET /api/documents/:id
// @access  Private
export const getDocumentById = asyncHandler(async (req, res) => {
  const document = await Document.findById(req.params.id)
    .populate('uploadedBy', 'name email')
    .populate('project', 'title client assignedVendor');
  
  if (!document || document.isDeleted) {
    res.status(404);
    throw new Error('Document not found');
  }
  
  // Check authorization
  const isUploader = document.uploadedBy._id.toString() === req.user._id.toString();
  const isProjectOwner = document.project.client.toString() === req.user._id.toString();
  const isAssignedVendor = document.project.assignedVendor && 
    document.project.assignedVendor.toString() === req.user._id.toString();
  const isAdmin = req.user.role === 'admin';
  const hasPermission = document.permissions.includes(req.user._id.toString());
  
  if (!isUploader && !isProjectOwner && !isAssignedVendor && !isAdmin && !hasPermission) {
    res.status(403);
    throw new Error('Not authorized to view this document');
  }
  
  try {
    // Generate signed URL for the document
    const getCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: document.fileKey
    });
    
    const signedUrl = await getSignedUrl(s3, getCommand, { expiresIn: 3600 });
    
    res.json({
      ...document.toObject(),
      url: signedUrl
    });
  } catch (error) {
    res.status(500);
    throw new Error('Could not generate document URL');
  }
});

// @desc    Extract embedded data from document using RDH
// @route   POST /api/documents/:id/extract
// @access  Private
export const extractDocumentData = asyncHandler(async (req, res) => {
  const document = await Document.findById(req.params.id);
  
  if (!document || document.isDeleted) {
    res.status(404);
    throw new Error('Document not found');
  }
  
  // Check authorization
  const isUploader = document.uploadedBy.toString() === req.user._id.toString();
  const isProjectOwner = await Project.exists({ 
    _id: document.project, 
    client: req.user._id 
  });
  const isAdmin = req.user.role === 'admin';
  
  if (!isUploader && !isProjectOwner && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to extract data from this document');
  }
  
  try {
    // Download the file from S3 to a temporary location
    const getCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: document.fileKey
    });
    
    const { Body } = await s3.send(getCommand);
    
    if (!Body) {
      throw new Error('Could not retrieve document from storage');
    }
    
    // Create temporary file
    const tempFilePath = path.join(os.tmpdir(), `extract-${uuidv4()}${path.extname(document.fileName)}`);
    const writeStream = fs.createWriteStream(tempFilePath);
    
    // Write the file to disk
    await new Promise((resolve, reject) => {
      Body.pipe(writeStream)
        .on('error', reject)
        .on('finish', resolve);
    });
    
    // Extract the embedded data
    const extractedData = await extractDataFromDocument(tempFilePath, document.securityLevel);
    
    // Clean up the temporary file
    fs.unlinkSync(tempFilePath);
    
    res.json({ 
      success: true, 
      data: extractedData,
      documentId: document._id
    });
    
  } catch (error) {
    res.status(500);
    throw new Error(`Data extraction failed: ${error.message}`);
  }
});

// @desc    Update document permissions
// @route   PUT /api/documents/:id/permissions
// @access  Private
export const updateDocumentPermissions = asyncHandler(async (req, res) => {
  const { permissions } = req.body;
  
  if (!permissions || !Array.isArray(permissions)) {
    res.status(400);
    throw new Error('Valid permissions array is required');
  }
  
  const document = await Document.findById(req.params.id);
  
  if (!document || document.isDeleted) {
    res.status(404);
    throw new Error('Document not found');
  }
  
  // Check authorization
  const isUploader = document.uploadedBy.toString() === req.user._id.toString();
  const isProjectOwner = await Project.exists({ 
    _id: document.project, 
    client: req.user._id 
  });
  const isAdmin = req.user.role === 'admin';
  
  if (!isUploader && !isProjectOwner && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to update permissions for this document');
  }
  
  // Update permissions
  document.permissions = permissions;
  const updatedDocument = await document.save();
  
  res.json(updatedDocument);
});

// @desc    Delete document (soft delete)
// @route   DELETE /api/documents/:id
// @access  Private
export const deleteDocument = asyncHandler(async (req, res) => {
  const document = await Document.findById(req.params.id);
  
  if (!document) {
    res.status(404);
    throw new Error('Document not found');
  }
  
  // Check authorization
  const isUploader = document.uploadedBy.toString() === req.user._id.toString();
  const isProjectOwner = await Project.exists({ 
    _id: document.project, 
    client: req.user._id 
  });
  const isAdmin = req.user.role === 'admin';
  
  if (!isUploader && !isProjectOwner && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to delete this document');
  }
  
  // Soft delete
  document.isDeleted = true;
  document.deletedAt = new Date();
  document.deletedBy = req.user._id;
  
  await document.save();
  
  res.json({ message: 'Document deleted successfully' });
});

// @desc    Update document version
// @route   PUT /api/documents/:id/version
// @access  Private
export const updateDocumentVersion = asyncHandler(async (req, res) => {
  const { description } = req.body;
  
  if (!req.file) {
    res.status(400);
    throw new Error('Please upload a file');
  }
  
  const document = await Document.findById(req.params.id);
  
  if (!document || document.isDeleted) {
    res.status(404);
    throw new Error('Document not found');
  }
  
  // Check authorization
  const isUploader = document.uploadedBy.toString() === req.user._id.toString();
  const isProjectOwner = await Project.exists({ 
    _id: document.project, 
    client: req.user._id 
  });
  const isAdmin = req.user.role === 'admin';
  
  if (!isUploader && !isProjectOwner && !isAdmin) {
    res.status(403);
    throw new Error('Not authorized to update this document');
  }
  
  // Path to the uploaded file
  const filePath = req.file.path;
  let securedFilePath;
  
  try {
    // Apply RDH to embed data in the document
    securedFilePath = await embedDataInDocument(
      filePath, 
      document.metadata, 
      document.securityLevel
    );
    
    // Create a new version entry
    document.versions.push({
      version: document.versions.length + 1,
      fileKey: document.fileKey,
      updatedBy: req.user._id,
      description: description || 'Document updated'
    });
    
    // Create a unique key for S3
    const fileKey = `${document.project}/${uuidv4()}-${path.basename(req.file.originalname)}`;
    
    // Upload to S3
    const uploadParams = {
      Bucket: bucketName,
      Key: fileKey,
      Body: fs.createReadStream(securedFilePath),
      ContentType: req.file.mimetype
    };
    
    await s3.send(new PutObjectCommand(uploadParams));
    
    // Update document record
    document.fileKey = fileKey;
    document.fileType = req.file.mimetype;
    document.fileName = req.file.originalname;
    document.fileSize = req.file.size;
    document.updatedBy = req.user._id;
    
    const updatedDocument = await document.save();
    
    // Generate temporary signed URL for immediate access
    const getCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileKey
    });
    
    const signedUrl = await getSignedUrl(s3, getCommand, { expiresIn: 3600 });
    
    // Clean up temporary files
    fs.unlinkSync(filePath);
    fs.unlinkSync(securedFilePath);
    
    res.json({
      document: updatedDocument,
      url: signedUrl
    });
    
  } catch (error) {
    // Clean up files on error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    if (securedFilePath && fs.existsSync(securedFilePath)) {
      fs.unlinkSync(securedFilePath);
    }
    
    res.status(500);
    throw new Error(`Document update failed: ${error.message}`);
  }
}); 