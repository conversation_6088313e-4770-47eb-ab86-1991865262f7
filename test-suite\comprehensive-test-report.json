{"summary": {"startTime": "2025-07-21T12:56:49.934Z", "endTime": "2025-07-21T12:57:12.680Z", "duration": 22746, "totalSuites": 6, "totalTests": 48, "totalPassed": 34, "totalFailed": 14, "overallSuccessRate": "70.83"}, "suites": [{"name": "Project Management", "icon": "📋", "total": 8, "passed": 6, "failed": 2, "successRate": "75.00", "tests": [{"name": "Browse Projects", "description": "Test browsing 8+ demo projects across different categories", "startTime": "2025-07-21T12:56:50.464Z", "logs": [{"timestamp": "2025-07-21T12:56:50.464Z", "type": "test", "message": "Starting test: Browse Projects - Test browsing 8+ demo projects across different categories"}, {"timestamp": "2025-07-21T12:56:50.642Z", "type": "info", "message": "Found 9 projects for client"}, {"timestamp": "2025-07-21T12:56:50.643Z", "type": "info", "message": "Found 6 different project categories: marketing, web-development, design, mobile-development, data-analytics, content"}, {"timestamp": "2025-07-21T12:56:50.775Z", "type": "info", "message": "<PERSON><PERSON><PERSON> can see 9 projects"}, {"timestamp": "2025-07-21T12:56:50.776Z", "type": "success", "message": "Test PASSED: Browse Projects"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:50.775Z", "duration": 311}, {"name": "Create Project", "description": "Test creating new projects with budget and timeline", "startTime": "2025-07-21T12:56:50.777Z", "logs": [{"timestamp": "2025-07-21T12:56:50.777Z", "type": "test", "message": "Starting test: Create Project - Test creating new projects with budget and timeline"}, {"timestamp": "2025-07-21T12:56:50.823Z", "type": "error", "message": "Request failed: POST /projects - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:56:50.823Z", "type": "error", "message": "Test failed: Request failed with status code 400"}, {"timestamp": "2025-07-21T12:56:50.824Z", "type": "error", "message": "Test FAILED: Create Project"}], "passed": false, "errors": ["Request failed with status code 400"], "endTime": "2025-07-21T12:56:50.823Z", "duration": 46}, {"name": "Project Details", "description": "Test viewing detailed project information", "startTime": "2025-07-21T12:56:50.824Z", "logs": [{"timestamp": "2025-07-21T12:56:50.824Z", "type": "test", "message": "Starting test: Project Details - Test viewing detailed project information"}, {"timestamp": "2025-07-21T12:56:51.084Z", "type": "info", "message": "Project details loaded for: Video Marketing Campaign"}, {"timestamp": "2025-07-21T12:56:51.301Z", "type": "info", "message": "Project details accessible to vendors"}, {"timestamp": "2025-07-21T12:56:51.302Z", "type": "success", "message": "Test PASSED: Project Details"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:51.302Z", "duration": 478}, {"name": "Update Project Status", "description": "Test managing project status and lifecycle", "startTime": "2025-07-21T12:56:51.303Z", "logs": [{"timestamp": "2025-07-21T12:56:51.303Z", "type": "test", "message": "Starting test: Update Project Status - Test managing project status and lifecycle"}, {"timestamp": "2025-07-21T12:56:51.555Z", "type": "error", "message": "Test failed: Project status was not updated"}, {"timestamp": "2025-07-21T12:56:51.556Z", "type": "error", "message": "Test FAILED: Update Project Status"}], "passed": false, "errors": ["Project status was not updated"], "endTime": "2025-07-21T12:56:51.555Z", "duration": 252}, {"name": "Project Milestones", "description": "Test project milestone management", "startTime": "2025-07-21T12:56:51.556Z", "logs": [{"timestamp": "2025-07-21T12:56:51.556Z", "type": "test", "message": "Starting test: Project Milestones - Test project milestone management"}, {"timestamp": "2025-07-21T12:56:51.728Z", "type": "error", "message": "Request failed: POST /projects/687e38ff5aa15396ad711afe/milestones - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:51.728Z", "type": "info", "message": "Milestone endpoint not implemented yet - this is expected"}, {"timestamp": "2025-07-21T12:56:51.774Z", "type": "error", "message": "Request failed: GET /projects/687e38ff5aa15396ad711afe/milestones - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:51.775Z", "type": "info", "message": "Milestone listing endpoint not implemented yet - this is expected"}, {"timestamp": "2025-07-21T12:56:51.776Z", "type": "success", "message": "Test PASSED: Project Milestones"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:51.776Z", "duration": 220}, {"name": "Project Analytics", "description": "Test project performance analytics", "startTime": "2025-07-21T12:56:51.776Z", "logs": [{"timestamp": "2025-07-21T12:56:51.776Z", "type": "test", "message": "Starting test: Project Analytics - Test project performance analytics"}, {"timestamp": "2025-07-21T12:56:51.996Z", "type": "info", "message": "Project analytics data retrieved"}, {"timestamp": "2025-07-21T12:56:51.997Z", "type": "info", "message": "Analytics fields available: total, byStatus, byCategory, projects"}, {"timestamp": "2025-07-21T12:56:52.180Z", "type": "error", "message": "Request failed: GET /projects/687e38ff5aa15396ad711afe/analytics - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:52.180Z", "type": "info", "message": "Individual project analytics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:56:52.181Z", "type": "success", "message": "Test PASSED: Project Analytics"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:52.181Z", "duration": 405}, {"name": "Project Categories", "description": "Test project categorization and filtering", "startTime": "2025-07-21T12:56:52.181Z", "logs": [{"timestamp": "2025-07-21T12:56:52.181Z", "type": "test", "message": "Starting test: Project Categories - Test project categorization and filtering"}, {"timestamp": "2025-07-21T12:56:52.305Z", "type": "info", "message": "Available categories: marketing, web-development, design, mobile-development, data-analytics, content"}, {"timestamp": "2025-07-21T12:56:52.450Z", "type": "info", "message": "Category filter 'marketing' working correctly (2 projects)"}, {"timestamp": "2025-07-21T12:56:52.629Z", "type": "info", "message": "Category filter 'web-development' working correctly (2 projects)"}, {"timestamp": "2025-07-21T12:56:52.629Z", "type": "success", "message": "Test PASSED: Project Categories"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:52.629Z", "duration": 448}, {"name": "Project Search", "description": "Test project search functionality", "startTime": "2025-07-21T12:56:52.630Z", "logs": [{"timestamp": "2025-07-21T12:56:52.630Z", "type": "test", "message": "Starting test: Project Search - Test project search functionality"}, {"timestamp": "2025-07-21T12:56:52.754Z", "type": "info", "message": "Search for 'web' returned 9 results"}, {"timestamp": "2025-07-21T12:56:52.755Z", "type": "info", "message": "2 results are relevant to search term 'web'"}, {"timestamp": "2025-07-21T12:56:52.880Z", "type": "info", "message": "Search for 'mobile' returned 9 results"}, {"timestamp": "2025-07-21T12:56:52.881Z", "type": "info", "message": "3 results are relevant to search term 'mobile'"}, {"timestamp": "2025-07-21T12:56:53.034Z", "type": "info", "message": "Search for 'design' returned 9 results"}, {"timestamp": "2025-07-21T12:56:53.034Z", "type": "info", "message": "2 results are relevant to search term 'design'"}, {"timestamp": "2025-07-21T12:56:53.035Z", "type": "success", "message": "Test PASSED: Project Search"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:53.035Z", "duration": 405}]}, {"name": "Bidding System", "icon": "💰", "total": 8, "passed": 4, "failed": 4, "successRate": "50.00", "tests": [{"name": "View Bids", "description": "Test viewing 20+ realistic bids on various projects", "startTime": "2025-07-21T12:56:53.747Z", "logs": [{"timestamp": "2025-07-21T12:56:53.747Z", "type": "test", "message": "Starting test: View Bids - Test viewing 20+ realistic bids on various projects"}, {"timestamp": "2025-07-21T12:56:54.003Z", "type": "info", "message": "Found 15 total bids in system"}, {"timestamp": "2025-07-21T12:56:54.003Z", "type": "info", "message": "Warning: Expected at least 20 bids, found 15"}, {"timestamp": "2025-07-21T12:56:54.004Z", "type": "info", "message": "Bid structure validation passed"}, {"timestamp": "2025-07-21T12:56:54.173Z", "type": "error", "message": "Request failed: GET /projects/687e38ff5aa15396ad711afe/bids - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:54.174Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:54.175Z", "type": "error", "message": "Test FAILED: View Bids"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:56:54.174Z", "duration": 427}, {"name": "Submit Bid", "description": "Test submitting detailed proposals", "startTime": "2025-07-21T12:56:54.176Z", "logs": [{"timestamp": "2025-07-21T12:56:54.176Z", "type": "test", "message": "Starting test: Submit Bid - Test submitting detailed proposals"}, {"timestamp": "2025-07-21T12:56:54.424Z", "type": "error", "message": "Request failed: POST /bids - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:56:54.425Z", "type": "error", "message": "Test failed: Request failed with status code 400"}, {"timestamp": "2025-07-21T12:56:54.427Z", "type": "error", "message": "Test FAILED: Submit Bid"}], "passed": false, "errors": ["Request failed with status code 400"], "endTime": "2025-07-21T12:56:54.425Z", "duration": 249}, {"name": "Bid with Attachments", "description": "Test submitting proposals with file attachments", "startTime": "2025-07-21T12:56:54.428Z", "logs": [{"timestamp": "2025-07-21T12:56:54.428Z", "type": "test", "message": "Starting test: Bid with Attachments - Test submitting proposals with file attachments"}, {"timestamp": "2025-07-21T12:56:54.632Z", "type": "error", "message": "Request failed: POST /bids - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:56:54.632Z", "type": "info", "message": "Attachment functionality may not be fully implemented yet"}, {"timestamp": "2025-07-21T12:56:54.633Z", "type": "success", "message": "Test PASSED: Bid with Attachments"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:54.633Z", "duration": 205}, {"name": "Accept Bid", "description": "Test accepting bids as a client", "startTime": "2025-07-21T12:56:54.634Z", "logs": [{"timestamp": "2025-07-21T12:56:54.634Z", "type": "test", "message": "Starting test: Accept Bid - Test accepting bids as a client"}, {"timestamp": "2025-07-21T12:56:54.843Z", "type": "error", "message": "Request failed: GET /projects/687e38ff5aa15396ad711afe/bids - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:54.844Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:54.845Z", "type": "error", "message": "Test FAILED: Accept Bid"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:56:54.844Z", "duration": 210}, {"name": "Reject Bid", "description": "Test rejecting bids as a client", "startTime": "2025-07-21T12:56:54.845Z", "logs": [{"timestamp": "2025-07-21T12:56:54.845Z", "type": "test", "message": "Starting test: Reject Bid - Test rejecting bids as a client"}, {"timestamp": "2025-07-21T12:56:55.052Z", "type": "error", "message": "Request failed: GET /projects/687e38ff5aa15396ad711afe/bids - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:55.053Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:55.054Z", "type": "error", "message": "Test FAILED: Reject Bid"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:56:55.053Z", "duration": 208}, {"name": "Bid Status Tracking", "description": "Test tracking bid status and changes", "startTime": "2025-07-21T12:56:55.054Z", "logs": [{"timestamp": "2025-07-21T12:56:55.054Z", "type": "test", "message": "Starting test: Bid Status Tracking - Test tracking bid status and changes"}, {"timestamp": "2025-07-21T12:56:55.257Z", "type": "info", "message": "Bid status distribution: {\"pending\":7,\"accepted\":1}"}, {"timestamp": "2025-07-21T12:56:55.492Z", "type": "info", "message": "Status filter 'pending' working correctly (7 bids)"}, {"timestamp": "2025-07-21T12:56:55.743Z", "type": "info", "message": "Status filter 'accepted' working correctly (1 bids)"}, {"timestamp": "2025-07-21T12:56:55.744Z", "type": "success", "message": "Test PASSED: Bid Status Tracking"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:55.744Z", "duration": 690}, {"name": "Bid Negotiation", "description": "Test bid negotiation and communication", "startTime": "2025-07-21T12:56:55.745Z", "logs": [{"timestamp": "2025-07-21T12:56:55.745Z", "type": "test", "message": "Starting test: Bid Negotiation - Test bid negotiation and communication"}, {"timestamp": "2025-07-21T12:56:56.083Z", "type": "error", "message": "Request failed: POST /bids/687e38ff5aa15396ad711b15/negotiate - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:56.083Z", "type": "info", "message": "Bid negotiation endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:56:56.192Z", "type": "error", "message": "Request failed: GET /bids/687e38ff5aa15396ad711b15/negotiations - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:56.192Z", "type": "info", "message": "Negotiation history endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:56:56.193Z", "type": "success", "message": "Test PASSED: Bid Negotiation"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:56.193Z", "duration": 448}, {"name": "<PERSON><PERSON><PERSON>", "description": "Test vendor bid history and analytics", "startTime": "2025-07-21T12:56:56.193Z", "logs": [{"timestamp": "2025-07-21T12:56:56.193Z", "type": "test", "message": "Starting test: Vendor Bid History - Test vendor bid history and analytics"}, {"timestamp": "2025-07-21T12:56:56.391Z", "type": "info", "message": "Vendor has 8 total bids"}, {"timestamp": "2025-07-21T12:56:56.392Z", "type": "info", "message": "Bid statistics - Accepted: 1, Rejected: 0, Pending: 7"}, {"timestamp": "2025-07-21T12:56:56.392Z", "type": "info", "message": "Success rate: 12.50%"}, {"timestamp": "2025-07-21T12:56:56.475Z", "type": "error", "message": "Request failed: GET /analytics/bids - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:56.475Z", "type": "info", "message": "Bid analytics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:56:56.476Z", "type": "success", "message": "Test PASSED: <PERSON><PERSON><PERSON>"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:56.476Z", "duration": 283}]}, {"name": "Messaging System", "icon": "💬", "total": 8, "passed": 5, "failed": 3, "successRate": "62.50", "tests": [{"name": "View Messages", "description": "Test viewing existing messages and conversations", "startTime": "2025-07-21T12:56:57.108Z", "logs": [{"timestamp": "2025-07-21T12:56:57.108Z", "type": "test", "message": "Starting test: View Messages - Test viewing existing messages and conversations"}, {"timestamp": "2025-07-21T12:56:57.401Z", "type": "info", "message": "Found 16 messages for client"}, {"timestamp": "2025-07-21T12:56:57.402Z", "type": "error", "message": "Test failed: Message missing required field: recipient"}, {"timestamp": "2025-07-21T12:56:57.403Z", "type": "error", "message": "Test FAILED: View Messages"}], "passed": false, "errors": ["Message missing required field: recipient"], "endTime": "2025-07-21T12:56:57.402Z", "duration": 294}, {"name": "Send Message", "description": "Test sending messages between users", "startTime": "2025-07-21T12:56:57.404Z", "logs": [{"timestamp": "2025-07-21T12:56:57.404Z", "type": "test", "message": "Starting test: Send Message - Test sending messages between users"}, {"timestamp": "2025-07-21T12:56:57.486Z", "type": "error", "message": "Request failed: POST /messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:57.486Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:57.487Z", "type": "error", "message": "Test FAILED: Send Message"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:56:57.486Z", "duration": 82}, {"name": "Message with Attachments", "description": "Test sending messages with file attachments", "startTime": "2025-07-21T12:56:57.487Z", "logs": [{"timestamp": "2025-07-21T12:56:57.487Z", "type": "test", "message": "Starting test: Message with At<PERSON>chments - Test sending messages with file attachments"}, {"timestamp": "2025-07-21T12:56:57.585Z", "type": "error", "message": "Request failed: POST /messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:57.585Z", "type": "info", "message": "Message attachment functionality may not be fully implemented yet"}, {"timestamp": "2025-07-21T12:56:57.586Z", "type": "success", "message": "Test PASSED: Message with Attachments"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:57.586Z", "duration": 99}, {"name": "Project Conversations", "description": "Test project-specific messaging", "startTime": "2025-07-21T12:56:57.587Z", "logs": [{"timestamp": "2025-07-21T12:56:57.587Z", "type": "test", "message": "Starting test: Project Conversations - Test project-specific messaging"}, {"timestamp": "2025-07-21T12:56:57.791Z", "type": "error", "message": "Request failed: POST /messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:57.792Z", "type": "info", "message": "Project-specific messaging may not be fully implemented"}, {"timestamp": "2025-07-21T12:56:57.835Z", "type": "error", "message": "Request failed: GET /projects/687e38ff5aa15396ad711afe/messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:57.836Z", "type": "info", "message": "Project message listing endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:56:57.837Z", "type": "success", "message": "Test PASSED: Project Conversations"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:57.837Z", "duration": 250}, {"name": "Group Conversations", "description": "Test group messaging for team collaboration", "startTime": "2025-07-21T12:56:57.838Z", "logs": [{"timestamp": "2025-07-21T12:56:57.838Z", "type": "test", "message": "Starting test: Group Conversations - Test group messaging for team collaboration"}, {"timestamp": "2025-07-21T12:56:57.922Z", "type": "error", "message": "Request failed: POST /messages/groups - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:57.923Z", "type": "info", "message": "Group conversation functionality not implemented yet"}, {"timestamp": "2025-07-21T12:56:58.006Z", "type": "error", "message": "Request failed: GET /messages/groups - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:58.006Z", "type": "info", "message": "Group listing endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:56:58.007Z", "type": "success", "message": "Test PASSED: Group Conversations"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:58.007Z", "duration": 169}, {"name": "Message Delivery", "description": "Test real-time message delivery and read receipts", "startTime": "2025-07-21T12:56:58.008Z", "logs": [{"timestamp": "2025-07-21T12:56:58.008Z", "type": "test", "message": "Starting test: Message Delivery - Test real-time message delivery and read receipts"}, {"timestamp": "2025-07-21T12:56:58.100Z", "type": "error", "message": "Request failed: POST /messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:58.100Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:58.101Z", "type": "error", "message": "Test FAILED: Message Delivery"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:56:58.100Z", "duration": 92}, {"name": "Message Search", "description": "Test searching messages by content and metadata", "startTime": "2025-07-21T12:56:58.102Z", "logs": [{"timestamp": "2025-07-21T12:56:58.102Z", "type": "test", "message": "Starting test: Message Search - Test searching messages by content and metadata"}, {"timestamp": "2025-07-21T12:56:58.344Z", "type": "info", "message": "Search for 'project' returned 3 results"}, {"timestamp": "2025-07-21T12:56:58.345Z", "type": "info", "message": "3 results are relevant to search term 'project'"}, {"timestamp": "2025-07-21T12:56:58.584Z", "type": "info", "message": "Search for 'requirements' returned 3 results"}, {"timestamp": "2025-07-21T12:56:58.585Z", "type": "info", "message": "3 results are relevant to search term 'requirements'"}, {"timestamp": "2025-07-21T12:56:58.859Z", "type": "info", "message": "Search for 'timeline' returned 2 results"}, {"timestamp": "2025-07-21T12:56:58.859Z", "type": "info", "message": "2 results are relevant to search term 'timeline'"}, {"timestamp": "2025-07-21T12:56:59.152Z", "type": "info", "message": "Found 16 messages from the last week"}, {"timestamp": "2025-07-21T12:56:59.153Z", "type": "success", "message": "Test PASSED: Message Search"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:59.153Z", "duration": 1051}, {"name": "Conversation Management", "description": "Test conversation organization and management", "startTime": "2025-07-21T12:56:59.154Z", "logs": [{"timestamp": "2025-07-21T12:56:59.154Z", "type": "test", "message": "Starting test: Conversation Management - Test conversation organization and management"}, {"timestamp": "2025-07-21T12:56:59.725Z", "type": "error", "message": "Request failed: GET /messages/unread/count - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:56:59.725Z", "type": "info", "message": "Unread count endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:56:59.726Z", "type": "success", "message": "Test PASSED: Conversation Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:56:59.726Z", "duration": 572}]}, {"name": "Document Management", "icon": "📁", "total": 8, "passed": 7, "failed": 1, "successRate": "87.50", "tests": [{"name": "View Documents", "description": "Test viewing 30+ demo documents across file types", "startTime": "2025-07-21T12:57:00.287Z", "logs": [{"timestamp": "2025-07-21T12:57:00.287Z", "type": "test", "message": "Starting test: View Documents - Test viewing 30+ demo documents across file types"}, {"timestamp": "2025-07-21T12:57:00.569Z", "type": "info", "message": "Found 20 total documents in system"}, {"timestamp": "2025-07-21T12:57:00.570Z", "type": "info", "message": "Warning: Expected at least 30 documents, found 20"}, {"timestamp": "2025-07-21T12:57:00.570Z", "type": "error", "message": "Test failed: Document missing required field: name"}, {"timestamp": "2025-07-21T12:57:00.571Z", "type": "error", "message": "Test FAILED: View Documents"}], "passed": false, "errors": ["Document missing required field: name"], "endTime": "2025-07-21T12:57:00.570Z", "duration": 283}, {"name": "Upload Document", "description": "Test uploading documents with metadata", "startTime": "2025-07-21T12:57:00.572Z", "logs": [{"timestamp": "2025-07-21T12:57:00.572Z", "type": "test", "message": "Starting test: Upload Document - Test uploading documents with metadata"}, {"timestamp": "2025-07-21T12:57:00.713Z", "type": "error", "message": "Request failed: POST /documents - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:57:00.714Z", "type": "info", "message": "Document upload endpoint may not be fully implemented yet"}, {"timestamp": "2025-07-21T12:57:00.715Z", "type": "success", "message": "Test PASSED: Upload Document"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:00.715Z", "duration": 143}, {"name": "Security Levels", "description": "Test document security levels and access control", "startTime": "2025-07-21T12:57:00.715Z", "logs": [{"timestamp": "2025-07-21T12:57:00.715Z", "type": "test", "message": "Starting test: Security Levels - Test document security levels and access control"}, {"timestamp": "2025-07-21T12:57:01.045Z", "type": "info", "message": "Security level distribution: {\"1\":7,\"2\":11,\"3\":2}"}, {"timestamp": "2025-07-21T12:57:01.127Z", "type": "error", "message": "Request failed: POST /documents - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:57:01.128Z", "type": "info", "message": "Security level public creation failed or not implemented"}, {"timestamp": "2025-07-21T12:57:01.209Z", "type": "error", "message": "Request failed: POST /documents - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:57:01.209Z", "type": "info", "message": "Security level private creation failed or not implemented"}, {"timestamp": "2025-07-21T12:57:01.310Z", "type": "error", "message": "Request failed: POST /documents - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:57:01.310Z", "type": "info", "message": "Security level confidential creation failed or not implemented"}, {"timestamp": "2025-07-21T12:57:01.312Z", "type": "success", "message": "Test PASSED: Security Levels"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:01.312Z", "duration": 597}, {"name": "Version Control", "description": "Test document version control and change tracking", "startTime": "2025-07-21T12:57:01.312Z", "logs": [{"timestamp": "2025-07-21T12:57:01.312Z", "type": "test", "message": "Starting test: Version Control - Test document version control and change tracking"}, {"timestamp": "2025-07-21T12:57:01.666Z", "type": "error", "message": "Request failed: POST /documents/687e39005aa15396ad711ba3/versions - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:01.666Z", "type": "info", "message": "Document versioning endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:01.750Z", "type": "error", "message": "Request failed: GET /documents/687e39005aa15396ad711ba3/versions - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:01.751Z", "type": "info", "message": "Version history endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:01.837Z", "type": "error", "message": "Request failed: GET /documents/687e39005aa15396ad711ba3/versions - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:01.837Z", "type": "info", "message": "Version comparison endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:01.838Z", "type": "success", "message": "Test PASSED: Version Control"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:01.838Z", "duration": 526}, {"name": "Role-based Access", "description": "Test role-based document access permissions", "startTime": "2025-07-21T12:57:01.838Z", "logs": [{"timestamp": "2025-07-21T12:57:01.838Z", "type": "test", "message": "Starting test: Role-based Access - Test role-based document access permissions"}, {"timestamp": "2025-07-21T12:57:02.084Z", "type": "info", "message": "<PERSON><PERSON> can see 20 documents"}, {"timestamp": "2025-07-21T12:57:02.361Z", "type": "info", "message": "Client can see 20 documents"}, {"timestamp": "2025-07-21T12:57:02.609Z", "type": "info", "message": "<PERSON><PERSON><PERSON> can see 8 documents"}, {"timestamp": "2025-07-21T12:57:02.810Z", "type": "error", "message": "Request failed: GET /documents/687e39005aa15396ad711ba3/permissions - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:02.810Z", "type": "info", "message": "Document permissions endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:02.889Z", "type": "error", "message": "Request failed: PUT /documents/687e39005aa15396ad711ba3/permissions - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:57:02.890Z", "type": "info", "message": "Permission update endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:03.095Z", "type": "error", "message": "Request failed: GET /projects/687e38ff5aa15396ad711afe/documents - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:03.096Z", "type": "info", "message": "Project document access endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:03.097Z", "type": "success", "message": "Test PASSED: Role-based Access"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:03.097Z", "duration": 1259}, {"name": "Document Types", "description": "Test handling of different document file types", "startTime": "2025-07-21T12:57:03.097Z", "logs": [{"timestamp": "2025-07-21T12:57:03.097Z", "type": "test", "message": "Starting test: Document Types - Test handling of different document file types"}, {"timestamp": "2025-07-21T12:57:03.394Z", "type": "info", "message": "File type analysis: {\n  \"unknown\": 20\n}"}, {"timestamp": "2025-07-21T12:57:03.394Z", "type": "info", "message": "Missing file types: application/pdf, image/png, image/jpeg, text/plain, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/zip"}, {"timestamp": "2025-07-21T12:57:03.689Z", "type": "info", "message": "Type filtering for 'unknown' not implemented or failed"}, {"timestamp": "2025-07-21T12:57:03.690Z", "type": "success", "message": "Test PASSED: Document Types"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:03.690Z", "duration": 593}, {"name": "Document Search", "description": "Test document search and filtering capabilities", "startTime": "2025-07-21T12:57:03.691Z", "logs": [{"timestamp": "2025-07-21T12:57:03.691Z", "type": "test", "message": "Starting test: Document Search - Test document search and filtering capabilities"}, {"timestamp": "2025-07-21T12:57:03.938Z", "type": "info", "message": "Search for 'specification' returned 8 results"}, {"timestamp": "2025-07-21T12:57:03.938Z", "type": "info", "message": "Document search for 'specification' not implemented or failed"}, {"timestamp": "2025-07-21T12:57:04.253Z", "type": "info", "message": "Search for 'design' returned 7 results"}, {"timestamp": "2025-07-21T12:57:04.254Z", "type": "info", "message": "Document search for 'design' not implemented or failed"}, {"timestamp": "2025-07-21T12:57:04.449Z", "type": "info", "message": "Search for 'contract' returned 0 results"}, {"timestamp": "2025-07-21T12:57:04.449Z", "type": "info", "message": "0 results are relevant to search term 'contract'"}, {"timestamp": "2025-07-21T12:57:04.688Z", "type": "info", "message": "Found 0 documents with 'requirements' tag"}, {"timestamp": "2025-07-21T12:57:05.084Z", "type": "info", "message": "Found 20 documents from the last month"}, {"timestamp": "2025-07-21T12:57:05.085Z", "type": "success", "message": "Test PASSED: Document Search"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:05.085Z", "duration": 1394}, {"name": "Document Sharing", "description": "Test document sharing and collaboration features", "startTime": "2025-07-21T12:57:05.086Z", "logs": [{"timestamp": "2025-07-21T12:57:05.086Z", "type": "test", "message": "Starting test: Document Sharing - Test document sharing and collaboration features"}, {"timestamp": "2025-07-21T12:57:05.438Z", "type": "error", "message": "Request failed: POST /documents/687e39005aa15396ad711ba3/share - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:05.439Z", "type": "info", "message": "Document sharing endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:05.563Z", "type": "error", "message": "Request failed: POST /documents/687e39005aa15396ad711ba3/share-link - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:05.564Z", "type": "info", "message": "Shareable link generation not implemented yet"}, {"timestamp": "2025-07-21T12:57:05.668Z", "type": "error", "message": "Request failed: POST /documents/687e39005aa15396ad711ba3/comments - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:05.669Z", "type": "info", "message": "Document commenting not implemented yet"}, {"timestamp": "2025-07-21T12:57:05.670Z", "type": "success", "message": "Test PASSED: Document Sharing"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:05.670Z", "duration": 584}]}, {"name": "Analytics Dashboard", "icon": "📊", "total": 8, "passed": 5, "failed": 3, "successRate": "62.50", "tests": [{"name": "Dashboard Statistics", "description": "Test personalized dashboard statistics for different user types", "startTime": "2025-07-21T12:57:06.254Z", "logs": [{"timestamp": "2025-07-21T12:57:06.254Z", "type": "test", "message": "Starting test: Dashboard Statistics - Test personalized dashboard statistics for different user types"}, {"timestamp": "2025-07-21T12:57:06.533Z", "type": "info", "message": "Client dashboard data retrieved successfully"}, {"timestamp": "2025-07-21T12:57:06.534Z", "type": "info", "message": "Client dashboard fields: totalProjects, activeProjects, completedProjects, totalBids, role"}, {"timestamp": "2025-07-21T12:57:06.535Z", "type": "info", "message": "Missing client dashboard fields: totalSpent, averageProjectCost"}, {"timestamp": "2025-07-21T12:57:06.796Z", "type": "info", "message": "Vendor dashboard data retrieved successfully"}, {"timestamp": "2025-07-21T12:57:06.796Z", "type": "info", "message": "Vendor dashboard fields: totalBids, acceptedBids, pendingBids, activeProjects, role"}, {"timestamp": "2025-07-21T12:57:06.797Z", "type": "info", "message": "Missing vendor dashboard fields: totalEarnings, successRate"}, {"timestamp": "2025-07-21T12:57:06.875Z", "type": "info", "message": "Admin dashboard data retrieved successfully"}, {"timestamp": "2025-07-21T12:57:06.875Z", "type": "info", "message": "Admin dashboard fields: "}, {"timestamp": "2025-07-21T12:57:06.876Z", "type": "info", "message": "Missing admin dashboard fields: totalUsers, totalProjects, totalRevenue, platformGrowth"}, {"timestamp": "2025-07-21T12:57:06.876Z", "type": "success", "message": "Test PASSED: Dashboard Statistics"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:06.876Z", "duration": 622}, {"name": "Project Analytics", "description": "Test project performance analytics and insights", "startTime": "2025-07-21T12:57:06.877Z", "logs": [{"timestamp": "2025-07-21T12:57:06.877Z", "type": "test", "message": "Starting test: Project Analytics - Test project performance analytics and insights"}, {"timestamp": "2025-07-21T12:57:07.044Z", "type": "info", "message": "Project analytics data retrieved successfully"}, {"timestamp": "2025-07-21T12:57:07.045Z", "type": "info", "message": "Project analytics fields: total, byStatus, byCategory, projects"}, {"timestamp": "2025-07-21T12:57:07.252Z", "type": "error", "message": "Request failed: GET /analytics/projects/687e38ff5aa15396ad711afe - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:07.252Z", "type": "info", "message": "Individual project analytics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:07.378Z", "type": "error", "message": "Request failed: GET /analytics/projects/trends - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:07.379Z", "type": "info", "message": "Project trends analytics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:07.459Z", "type": "error", "message": "Request failed: GET /analytics/projects/categories - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:07.460Z", "type": "info", "message": "Project category analytics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:07.461Z", "type": "success", "message": "Test PASSED: Project Analytics"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:07.461Z", "duration": 584}, {"name": "Revenue Tracking", "description": "Test revenue and earnings tracking functionality", "startTime": "2025-07-21T12:57:07.461Z", "logs": [{"timestamp": "2025-07-21T12:57:07.461Z", "type": "test", "message": "Starting test: Revenue Tracking - Test revenue and earnings tracking functionality"}, {"timestamp": "2025-07-21T12:57:07.581Z", "type": "error", "message": "Request failed: GET /analytics/revenue - Request failed with status code 403"}, {"timestamp": "2025-07-21T12:57:07.582Z", "type": "error", "message": "Test failed: Request failed with status code 403"}, {"timestamp": "2025-07-21T12:57:07.582Z", "type": "error", "message": "Test FAILED: Revenue Tracking"}], "passed": false, "errors": ["Request failed with status code 403"], "endTime": "2025-07-21T12:57:07.582Z", "duration": 121}, {"name": "User Engagement", "description": "Test user activity and engagement metrics", "startTime": "2025-07-21T12:57:07.583Z", "logs": [{"timestamp": "2025-07-21T12:57:07.583Z", "type": "test", "message": "Starting test: User Engagement - Test user activity and engagement metrics"}, {"timestamp": "2025-07-21T12:57:07.660Z", "type": "error", "message": "Request failed: GET /analytics/user/activity - Request failed with status code 500"}, {"timestamp": "2025-07-21T12:57:07.660Z", "type": "error", "message": "Test failed: Request failed with status code 500"}, {"timestamp": "2025-07-21T12:57:07.661Z", "type": "error", "message": "Test FAILED: User Engagement"}], "passed": false, "errors": ["Request failed with status code 500"], "endTime": "2025-07-21T12:57:07.660Z", "duration": 77}, {"name": "Performance Metrics", "description": "Test system and user performance metrics", "startTime": "2025-07-21T12:57:07.662Z", "logs": [{"timestamp": "2025-07-21T12:57:07.662Z", "type": "test", "message": "Starting test: Performance Metrics - Test system and user performance metrics"}, {"timestamp": "2025-07-21T12:57:07.741Z", "type": "info", "message": "System performance metrics retrieved successfully"}, {"timestamp": "2025-07-21T12:57:07.742Z", "type": "info", "message": "System metrics: responseTime, throughput, errorRate, uptime, memoryUsage, cpuUsage"}, {"timestamp": "2025-07-21T12:57:07.822Z", "type": "error", "message": "Request failed: GET /analytics/user/performance - Request failed with status code 500"}, {"timestamp": "2025-07-21T12:57:07.823Z", "type": "error", "message": "Test failed: Request failed with status code 500"}, {"timestamp": "2025-07-21T12:57:07.823Z", "type": "error", "message": "Test FAILED: Performance Metrics"}], "passed": false, "errors": ["Request failed with status code 500"], "endTime": "2025-07-21T12:57:07.823Z", "duration": 161}, {"name": "Analytics Filtering", "description": "Test filtering and date range functionality in analytics", "startTime": "2025-07-21T12:57:07.824Z", "logs": [{"timestamp": "2025-07-21T12:57:07.824Z", "type": "test", "message": "Starting test: Analytics Filtering - Test filtering and date range functionality in analytics"}, {"timestamp": "2025-07-21T12:57:08.086Z", "type": "info", "message": "Analytics for Last 7 days retrieved successfully"}, {"timestamp": "2025-07-21T12:57:08.386Z", "type": "info", "message": "Analytics for Last 30 days retrieved successfully"}, {"timestamp": "2025-07-21T12:57:08.648Z", "type": "info", "message": "Analytics for Last 3 months retrieved successfully"}, {"timestamp": "2025-07-21T12:57:09.029Z", "type": "info", "message": "Analytics for Last year retrieved successfully"}, {"timestamp": "2025-07-21T12:57:09.314Z", "type": "info", "message": "Custom date range analytics retrieved successfully"}, {"timestamp": "2025-07-21T12:57:09.507Z", "type": "info", "message": "Category-filtered analytics retrieved successfully"}, {"timestamp": "2025-07-21T12:57:09.659Z", "type": "info", "message": "Status-filtered analytics retrieved successfully"}, {"timestamp": "2025-07-21T12:57:09.659Z", "type": "success", "message": "Test PASSED: Analytics Filtering"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:09.659Z", "duration": 1835}, {"name": "Data Export", "description": "Test analytics data export functionality", "startTime": "2025-07-21T12:57:09.660Z", "logs": [{"timestamp": "2025-07-21T12:57:09.660Z", "type": "test", "message": "Starting test: Data Export - Test analytics data export functionality"}, {"timestamp": "2025-07-21T12:57:09.741Z", "type": "error", "message": "Request failed: GET /analytics/export?format=csv&type=projects - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:09.742Z", "type": "info", "message": "CSV export not implemented yet"}, {"timestamp": "2025-07-21T12:57:09.823Z", "type": "error", "message": "Request failed: GET /analytics/export?format=json&type=revenue - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:09.823Z", "type": "info", "message": "JSON export not implemented yet"}, {"timestamp": "2025-07-21T12:57:09.909Z", "type": "error", "message": "Request failed: POST /analytics/reports/generate - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:09.909Z", "type": "info", "message": "PDF report generation not implemented yet"}, {"timestamp": "2025-07-21T12:57:09.910Z", "type": "success", "message": "Test PASSED: Data Export"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:09.910Z", "duration": 250}, {"name": "Real-time Updates", "description": "Test real-time analytics updates and live data", "startTime": "2025-07-21T12:57:09.910Z", "logs": [{"timestamp": "2025-07-21T12:57:09.910Z", "type": "test", "message": "Starting test: Real-time Updates - Test real-time analytics updates and live data"}, {"timestamp": "2025-07-21T12:57:10.048Z", "type": "error", "message": "Request failed: GET /analytics/realtime - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:10.049Z", "type": "info", "message": "Real-time analytics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:10.130Z", "type": "error", "message": "Request failed: GET /analytics/live/metrics - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:10.130Z", "type": "info", "message": "Live metrics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:10.209Z", "type": "error", "message": "Request failed: POST /analytics/refresh - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:10.209Z", "type": "info", "message": "Analytics refresh endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:10.210Z", "type": "success", "message": "Test PASSED: Real-time Updates"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:10.210Z", "duration": 300}]}, {"name": "Admin Panel", "icon": "👑", "total": 8, "passed": 7, "failed": 1, "successRate": "87.50", "tests": [{"name": "User Management", "description": "Test user management and administration features", "startTime": "2025-07-21T12:57:10.532Z", "logs": [{"timestamp": "2025-07-21T12:57:10.532Z", "type": "test", "message": "Starting test: User Management - Test user management and administration features"}, {"timestamp": "2025-07-21T12:57:10.648Z", "type": "info", "message": "Found 9 total users in system"}, {"timestamp": "2025-07-21T12:57:10.648Z", "type": "info", "message": "User structure validation passed"}, {"timestamp": "2025-07-21T12:57:10.649Z", "type": "info", "message": "User role distribution: {\"client\":3,\"vendor\":5,\"admin\":1}"}, {"timestamp": "2025-07-21T12:57:10.779Z", "type": "info", "message": "User search returned 1 results"}, {"timestamp": "2025-07-21T12:57:10.895Z", "type": "info", "message": "Found 5 vendor users"}, {"timestamp": "2025-07-21T12:57:10.937Z", "type": "error", "message": "Request failed: GET /admin/users - Request failed with status code 403"}, {"timestamp": "2025-07-21T12:57:10.938Z", "type": "info", "message": "User management correctly restricted to admin users"}, {"timestamp": "2025-07-21T12:57:10.939Z", "type": "success", "message": "Test PASSED: User Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:10.939Z", "duration": 407}, {"name": "Role Assignment", "description": "Test user role management and permissions", "startTime": "2025-07-21T12:57:10.940Z", "logs": [{"timestamp": "2025-07-21T12:57:10.940Z", "type": "test", "message": "Starting test: Role Assignment - Test user role management and permissions"}, {"timestamp": "2025-07-21T12:57:11.060Z", "type": "info", "message": "Testing role assignment for user: <PERSON> (current role: vendor)"}, {"timestamp": "2025-07-21T12:57:11.204Z", "type": "info", "message": "Role assignment endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:11.246Z", "type": "error", "message": "Request failed: GET /admin/roles/permissions - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:11.246Z", "type": "info", "message": "Role permissions endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:11.291Z", "type": "error", "message": "Request failed: PUT /admin/users/bulk/role - Request failed with status code 500"}, {"timestamp": "2025-07-21T12:57:11.292Z", "type": "info", "message": "Bulk role assignment endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:11.292Z", "type": "success", "message": "Test PASSED: Role Assignment"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:11.292Z", "duration": 352}, {"name": "Platform Analytics", "description": "Test platform-wide analytics and reporting", "startTime": "2025-07-21T12:57:11.293Z", "logs": [{"timestamp": "2025-07-21T12:57:11.293Z", "type": "test", "message": "Starting test: Platform Analytics - Test platform-wide analytics and reporting"}, {"timestamp": "2025-07-21T12:57:11.372Z", "type": "error", "message": "Request failed: GET /admin/analytics/overview - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:11.372Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:11.372Z", "type": "error", "message": "Test FAILED: Platform Analytics"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:57:11.372Z", "duration": 79}, {"name": "System Configuration", "description": "Test system settings and configuration management", "startTime": "2025-07-21T12:57:11.373Z", "logs": [{"timestamp": "2025-07-21T12:57:11.373Z", "type": "test", "message": "Starting test: System Configuration - Test system settings and configuration management"}, {"timestamp": "2025-07-21T12:57:11.414Z", "type": "error", "message": "Request failed: GET /admin/config - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:11.414Z", "type": "info", "message": "System configuration endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:11.459Z", "type": "error", "message": "Request failed: PUT /admin/config - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:11.460Z", "type": "info", "message": "System configuration update endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:11.503Z", "type": "info", "message": "Feature flags retrieved successfully"}, {"timestamp": "2025-07-21T12:57:11.503Z", "type": "info", "message": "Available features: realTimeMessaging, advancedAnalytics, documentVersioning, aiRecommendations, multiLanguageSupport"}, {"timestamp": "2025-07-21T12:57:11.551Z", "type": "info", "message": "Feature flags updated successfully"}, {"timestamp": "2025-07-21T12:57:11.594Z", "type": "info", "message": "System limits retrieved successfully"}, {"timestamp": "2025-07-21T12:57:11.595Z", "type": "success", "message": "Test PASSED: System Configuration"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:11.595Z", "duration": 222}, {"name": "Content Moderation", "description": "Test content moderation and security features", "startTime": "2025-07-21T12:57:11.596Z", "logs": [{"timestamp": "2025-07-21T12:57:11.596Z", "type": "test", "message": "Starting test: Content Moderation - Test content moderation and security features"}, {"timestamp": "2025-07-21T12:57:11.637Z", "type": "error", "message": "Request failed: GET /admin/moderation/flagged - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:11.638Z", "type": "info", "message": "Flagged content endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:11.680Z", "type": "error", "message": "Request failed: GET /admin/moderation/flagged - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:11.680Z", "type": "info", "message": "Content approval endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:11.722Z", "type": "info", "message": "Found 1 user reports"}, {"timestamp": "2025-07-21T12:57:11.779Z", "type": "info", "message": "Content scan initiated successfully"}, {"timestamp": "2025-07-21T12:57:11.821Z", "type": "error", "message": "Request failed: GET /admin/moderation/rules - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:11.821Z", "type": "info", "message": "Content filtering rules endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:11.823Z", "type": "success", "message": "Test PASSED: Content Moderation"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:11.823Z", "duration": 227}, {"name": "Security Management", "description": "Test security monitoring and management features", "startTime": "2025-07-21T12:57:11.824Z", "logs": [{"timestamp": "2025-07-21T12:57:11.824Z", "type": "test", "message": "Starting test: Security Management - Test security monitoring and management features"}, {"timestamp": "2025-07-21T12:57:11.868Z", "type": "error", "message": "Request failed: GET /admin/security/logs - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:11.869Z", "type": "info", "message": "Security logs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:12.021Z", "type": "error", "message": "Request failed: GET /admin/security/failed-logins - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:12.022Z", "type": "info", "message": "Failed logins endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:12.063Z", "type": "error", "message": "Request failed: GET /admin/security/blocked-ips - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:12.063Z", "type": "info", "message": "Blocked IPs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:12.106Z", "type": "error", "message": "Request failed: GET /admin/security/alerts - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:12.107Z", "type": "info", "message": "Security alerts endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:12.107Z", "type": "success", "message": "Test PASSED: Security Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:12.107Z", "duration": 283}, {"name": "System Monitoring", "description": "Test system health and performance monitoring", "startTime": "2025-07-21T12:57:12.108Z", "logs": [{"timestamp": "2025-07-21T12:57:12.108Z", "type": "test", "message": "Starting test: System Monitoring - Test system health and performance monitoring"}, {"timestamp": "2025-07-21T12:57:12.149Z", "type": "error", "message": "Request failed: GET /admin/system/health - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:12.149Z", "type": "info", "message": "System health endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:12.194Z", "type": "error", "message": "Request failed: GET /admin/system/performance - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:12.195Z", "type": "info", "message": "Performance metrics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:12.239Z", "type": "error", "message": "Request failed: GET /admin/system/errors - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:57:12.239Z", "type": "info", "message": "Error logs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:57:12.281Z", "type": "info", "message": "System status retrieved successfully"}, {"timestamp": "2025-07-21T12:57:12.281Z", "type": "success", "message": "Test PASSED: System Monitoring"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:12.281Z", "duration": 173}, {"name": "Data Management", "description": "Test data backup, export, and maintenance features", "startTime": "2025-07-21T12:57:12.283Z", "logs": [{"timestamp": "2025-07-21T12:57:12.283Z", "type": "test", "message": "Starting test: Data Management - Test data backup, export, and maintenance features"}, {"timestamp": "2025-07-21T12:57:12.325Z", "type": "info", "message": "Data export initiated with ID: export_1753102632322"}, {"timestamp": "2025-07-21T12:57:12.367Z", "type": "info", "message": "Found 2 backup records"}, {"timestamp": "2025-07-21T12:57:12.409Z", "type": "info", "message": "Data cleanup initiated successfully"}, {"timestamp": "2025-07-21T12:57:12.674Z", "type": "info", "message": "Database statistics retrieved successfully"}, {"timestamp": "2025-07-21T12:57:12.674Z", "type": "success", "message": "Test PASSED: Data Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:57:12.674Z", "duration": 391}]}], "generatedAt": "2025-07-21T12:57:12.680Z"}