{"summary": {"startTime": "2025-07-21T12:27:01.517Z", "endTime": "2025-07-21T12:27:14.196Z", "duration": 12679, "totalSuites": 6, "totalTests": 48, "totalPassed": 16, "totalFailed": 32, "overallSuccessRate": "33.33"}, "suites": [{"name": "Project Management", "icon": "📋", "total": 8, "passed": 1, "failed": 7, "successRate": "12.50", "tests": [{"name": "Browse Projects", "description": "Test browsing 8+ demo projects across different categories", "startTime": "2025-07-21T12:27:02.092Z", "logs": [{"timestamp": "2025-07-21T12:27:02.092Z", "type": "test", "message": "Starting test: Browse Projects - Test browsing 8+ demo projects across different categories"}, {"timestamp": "2025-07-21T12:27:02.239Z", "type": "error", "message": "Test failed: Projects response is not an array"}, {"timestamp": "2025-07-21T12:27:02.240Z", "type": "error", "message": "Test FAILED: Browse Projects"}], "passed": false, "errors": ["Projects response is not an array"], "endTime": "2025-07-21T12:27:02.239Z", "duration": 147}, {"name": "Create Project", "description": "Test creating new projects with budget and timeline", "startTime": "2025-07-21T12:27:02.240Z", "logs": [{"timestamp": "2025-07-21T12:27:02.240Z", "type": "test", "message": "Starting test: Create Project - Test creating new projects with budget and timeline"}, {"timestamp": "2025-07-21T12:27:02.290Z", "type": "error", "message": "Request failed: POST /projects - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:27:02.290Z", "type": "error", "message": "Test failed: Request failed with status code 400"}, {"timestamp": "2025-07-21T12:27:02.290Z", "type": "error", "message": "Test FAILED: Create Project"}], "passed": false, "errors": ["Request failed with status code 400"], "endTime": "2025-07-21T12:27:02.290Z", "duration": 50}, {"name": "Project Details", "description": "Test viewing detailed project information", "startTime": "2025-07-21T12:27:02.291Z", "logs": [{"timestamp": "2025-07-21T12:27:02.291Z", "type": "test", "message": "Starting test: Project Details - Test viewing detailed project information"}, {"timestamp": "2025-07-21T12:27:02.423Z", "type": "error", "message": "Test failed: Cannot read properties of undefined (reading '_id')"}, {"timestamp": "2025-07-21T12:27:02.424Z", "type": "error", "message": "Test FAILED: Project Details"}], "passed": false, "errors": ["Cannot read properties of undefined (reading '_id')"], "endTime": "2025-07-21T12:27:02.423Z", "duration": 132}, {"name": "Update Project Status", "description": "Test managing project status and lifecycle", "startTime": "2025-07-21T12:27:02.424Z", "logs": [{"timestamp": "2025-07-21T12:27:02.424Z", "type": "test", "message": "Starting test: Update Project Status - Test managing project status and lifecycle"}, {"timestamp": "2025-07-21T12:27:02.606Z", "type": "error", "message": "Test failed: projects.filter is not a function"}, {"timestamp": "2025-07-21T12:27:02.606Z", "type": "error", "message": "Test FAILED: Update Project Status"}], "passed": false, "errors": ["projects.filter is not a function"], "endTime": "2025-07-21T12:27:02.606Z", "duration": 182}, {"name": "Project Milestones", "description": "Test project milestone management", "startTime": "2025-07-21T12:27:02.607Z", "logs": [{"timestamp": "2025-07-21T12:27:02.607Z", "type": "test", "message": "Starting test: Project Milestones - Test project milestone management"}, {"timestamp": "2025-07-21T12:27:02.789Z", "type": "error", "message": "Test failed: Cannot read properties of undefined (reading '_id')"}, {"timestamp": "2025-07-21T12:27:02.789Z", "type": "error", "message": "Test FAILED: Project Milestones"}], "passed": false, "errors": ["Cannot read properties of undefined (reading '_id')"], "endTime": "2025-07-21T12:27:02.789Z", "duration": 182}, {"name": "Project Analytics", "description": "Test project performance analytics", "startTime": "2025-07-21T12:27:02.790Z", "logs": [{"timestamp": "2025-07-21T12:27:02.790Z", "type": "test", "message": "Starting test: Project Analytics - Test project performance analytics"}, {"timestamp": "2025-07-21T12:27:02.880Z", "type": "error", "message": "Request failed: GET /analytics/projects - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:02.880Z", "type": "info", "message": "Project analytics endpoint may not be fully implemented"}, {"timestamp": "2025-07-21T12:27:03.040Z", "type": "error", "message": "Test failed: Cannot read properties of undefined (reading '_id')"}, {"timestamp": "2025-07-21T12:27:03.041Z", "type": "error", "message": "Test FAILED: Project Analytics"}], "passed": false, "errors": ["Cannot read properties of undefined (reading '_id')"], "endTime": "2025-07-21T12:27:03.040Z", "duration": 250}, {"name": "Project Categories", "description": "Test project categorization and filtering", "startTime": "2025-07-21T12:27:03.043Z", "logs": [{"timestamp": "2025-07-21T12:27:03.043Z", "type": "test", "message": "Starting test: Project Categories - Test project categorization and filtering"}, {"timestamp": "2025-07-21T12:27:03.239Z", "type": "error", "message": "Test failed: allProjects.map is not a function"}, {"timestamp": "2025-07-21T12:27:03.240Z", "type": "error", "message": "Test FAILED: Project Categories"}], "passed": false, "errors": ["allProjects.map is not a function"], "endTime": "2025-07-21T12:27:03.239Z", "duration": 196}, {"name": "Project Search", "description": "Test project search functionality", "startTime": "2025-07-21T12:27:03.240Z", "logs": [{"timestamp": "2025-07-21T12:27:03.240Z", "type": "test", "message": "Starting test: Project Search - Test project search functionality"}, {"timestamp": "2025-07-21T12:27:03.674Z", "type": "success", "message": "Test PASSED: Project Search"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:03.674Z", "duration": 434}]}, {"name": "Bidding System", "icon": "💰", "total": 8, "passed": 0, "failed": 8, "successRate": "0.00", "tests": [{"name": "View Bids", "description": "Test viewing 20+ realistic bids on various projects", "startTime": "2025-07-21T12:27:04.359Z", "logs": [{"timestamp": "2025-07-21T12:27:04.359Z", "type": "test", "message": "Starting test: View Bids - Test viewing 20+ realistic bids on various projects"}, {"timestamp": "2025-07-21T12:27:04.448Z", "type": "error", "message": "Request failed: GET /bids - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:04.448Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:04.449Z", "type": "error", "message": "Test FAILED: View Bids"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:04.448Z", "duration": 89}, {"name": "Submit Bid", "description": "Test submitting detailed proposals", "startTime": "2025-07-21T12:27:04.449Z", "logs": [{"timestamp": "2025-07-21T12:27:04.449Z", "type": "test", "message": "Starting test: Submit Bid - Test submitting detailed proposals"}, {"timestamp": "2025-07-21T12:27:04.644Z", "type": "error", "message": "Test failed: projects.filter is not a function"}, {"timestamp": "2025-07-21T12:27:04.645Z", "type": "error", "message": "Test FAILED: Submit Bid"}], "passed": false, "errors": ["projects.filter is not a function"], "endTime": "2025-07-21T12:27:04.644Z", "duration": 195}, {"name": "Bid with Attachments", "description": "Test submitting proposals with file attachments", "startTime": "2025-07-21T12:27:04.646Z", "logs": [{"timestamp": "2025-07-21T12:27:04.646Z", "type": "test", "message": "Starting test: Bid with Attachments - Test submitting proposals with file attachments"}, {"timestamp": "2025-07-21T12:27:04.825Z", "type": "error", "message": "Test failed: projects.filter is not a function"}, {"timestamp": "2025-07-21T12:27:04.826Z", "type": "error", "message": "Test FAILED: Bid with Attachments"}], "passed": false, "errors": ["projects.filter is not a function"], "endTime": "2025-07-21T12:27:04.825Z", "duration": 179}, {"name": "Accept Bid", "description": "Test accepting bids as a client", "startTime": "2025-07-21T12:27:04.827Z", "logs": [{"timestamp": "2025-07-21T12:27:04.827Z", "type": "test", "message": "Starting test: Accept Bid - Test accepting bids as a client"}, {"timestamp": "2025-07-21T12:27:04.956Z", "type": "error", "message": "Test failed: projects.filter is not a function"}, {"timestamp": "2025-07-21T12:27:04.957Z", "type": "error", "message": "Test FAILED: Accept Bid"}], "passed": false, "errors": ["projects.filter is not a function"], "endTime": "2025-07-21T12:27:04.956Z", "duration": 129}, {"name": "Reject Bid", "description": "Test rejecting bids as a client", "startTime": "2025-07-21T12:27:04.957Z", "logs": [{"timestamp": "2025-07-21T12:27:04.957Z", "type": "test", "message": "Starting test: Reject Bid - Test rejecting bids as a client"}, {"timestamp": "2025-07-21T12:27:05.088Z", "type": "error", "message": "Test failed: projects.filter is not a function"}, {"timestamp": "2025-07-21T12:27:05.088Z", "type": "error", "message": "Test FAILED: Reject Bid"}], "passed": false, "errors": ["projects.filter is not a function"], "endTime": "2025-07-21T12:27:05.088Z", "duration": 131}, {"name": "Bid Status Tracking", "description": "Test tracking bid status and changes", "startTime": "2025-07-21T12:27:05.089Z", "logs": [{"timestamp": "2025-07-21T12:27:05.089Z", "type": "test", "message": "Starting test: Bid Status Tracking - Test tracking bid status and changes"}, {"timestamp": "2025-07-21T12:27:05.225Z", "type": "error", "message": "Request failed: GET /bids - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:05.225Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:05.226Z", "type": "error", "message": "Test FAILED: Bid Status Tracking"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:05.225Z", "duration": 136}, {"name": "Bid Negotiation", "description": "Test bid negotiation and communication", "startTime": "2025-07-21T12:27:05.226Z", "logs": [{"timestamp": "2025-07-21T12:27:05.226Z", "type": "test", "message": "Starting test: Bid Negotiation - Test bid negotiation and communication"}, {"timestamp": "2025-07-21T12:27:05.317Z", "type": "error", "message": "Request failed: GET /bids - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:05.317Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:05.318Z", "type": "error", "message": "Test FAILED: Bid Negotiation"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:05.317Z", "duration": 91}, {"name": "<PERSON><PERSON><PERSON>", "description": "Test vendor bid history and analytics", "startTime": "2025-07-21T12:27:05.318Z", "logs": [{"timestamp": "2025-07-21T12:27:05.318Z", "type": "test", "message": "Starting test: Vendor Bid History - Test vendor bid history and analytics"}, {"timestamp": "2025-07-21T12:27:05.413Z", "type": "error", "message": "Request failed: GET /bids - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:05.413Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:05.414Z", "type": "error", "message": "Test FAILED: <PERSON><PERSON><PERSON>"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:05.413Z", "duration": 95}]}, {"name": "Messaging System", "icon": "💬", "total": 8, "passed": 4, "failed": 4, "successRate": "50.00", "tests": [{"name": "View Messages", "description": "Test viewing existing messages and conversations", "startTime": "2025-07-21T12:27:05.977Z", "logs": [{"timestamp": "2025-07-21T12:27:05.977Z", "type": "test", "message": "Starting test: View Messages - Test viewing existing messages and conversations"}, {"timestamp": "2025-07-21T12:27:06.061Z", "type": "error", "message": "Request failed: GET /messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:06.062Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:06.062Z", "type": "error", "message": "Test FAILED: View Messages"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:06.062Z", "duration": 85}, {"name": "Send Message", "description": "Test sending messages between users", "startTime": "2025-07-21T12:27:06.063Z", "logs": [{"timestamp": "2025-07-21T12:27:06.063Z", "type": "test", "message": "Starting test: Send Message - Test sending messages between users"}, {"timestamp": "2025-07-21T12:27:06.150Z", "type": "error", "message": "Request failed: POST /messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:06.151Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:06.151Z", "type": "error", "message": "Test FAILED: Send Message"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:06.151Z", "duration": 88}, {"name": "Message with Attachments", "description": "Test sending messages with file attachments", "startTime": "2025-07-21T12:27:06.152Z", "logs": [{"timestamp": "2025-07-21T12:27:06.152Z", "type": "test", "message": "Starting test: Message with At<PERSON>chments - Test sending messages with file attachments"}, {"timestamp": "2025-07-21T12:27:06.239Z", "type": "error", "message": "Request failed: POST /messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:06.239Z", "type": "info", "message": "Message attachment functionality may not be fully implemented yet"}, {"timestamp": "2025-07-21T12:27:06.240Z", "type": "success", "message": "Test PASSED: Message with Attachments"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:06.240Z", "duration": 88}, {"name": "Project Conversations", "description": "Test project-specific messaging", "startTime": "2025-07-21T12:27:06.240Z", "logs": [{"timestamp": "2025-07-21T12:27:06.240Z", "type": "test", "message": "Starting test: Project Conversations - Test project-specific messaging"}, {"timestamp": "2025-07-21T12:27:06.407Z", "type": "error", "message": "Test failed: Cannot read properties of undefined (reading '_id')"}, {"timestamp": "2025-07-21T12:27:06.407Z", "type": "error", "message": "Test FAILED: Project Conversations"}], "passed": false, "errors": ["Cannot read properties of undefined (reading '_id')"], "endTime": "2025-07-21T12:27:06.407Z", "duration": 167}, {"name": "Group Conversations", "description": "Test group messaging for team collaboration", "startTime": "2025-07-21T12:27:06.408Z", "logs": [{"timestamp": "2025-07-21T12:27:06.408Z", "type": "test", "message": "Starting test: Group Conversations - Test group messaging for team collaboration"}, {"timestamp": "2025-07-21T12:27:06.494Z", "type": "error", "message": "Request failed: POST /messages/groups - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:06.495Z", "type": "info", "message": "Group conversation functionality not implemented yet"}, {"timestamp": "2025-07-21T12:27:06.609Z", "type": "error", "message": "Request failed: GET /messages/groups - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:06.609Z", "type": "info", "message": "Group listing endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:06.609Z", "type": "success", "message": "Test PASSED: Group Conversations"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:06.609Z", "duration": 201}, {"name": "Message Delivery", "description": "Test real-time message delivery and read receipts", "startTime": "2025-07-21T12:27:06.610Z", "logs": [{"timestamp": "2025-07-21T12:27:06.610Z", "type": "test", "message": "Starting test: Message Delivery - Test real-time message delivery and read receipts"}, {"timestamp": "2025-07-21T12:27:06.698Z", "type": "error", "message": "Request failed: POST /messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:06.698Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:06.698Z", "type": "error", "message": "Test FAILED: Message Delivery"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:06.698Z", "duration": 88}, {"name": "Message Search", "description": "Test searching messages by content and metadata", "startTime": "2025-07-21T12:27:06.699Z", "logs": [{"timestamp": "2025-07-21T12:27:06.699Z", "type": "test", "message": "Starting test: Message Search - Test searching messages by content and metadata"}, {"timestamp": "2025-07-21T12:27:06.787Z", "type": "error", "message": "Request failed: GET /messages?search=project - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:06.787Z", "type": "info", "message": "Message search for 'project' not implemented or failed"}, {"timestamp": "2025-07-21T12:27:06.924Z", "type": "error", "message": "Request failed: GET /messages?search=requirements - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:06.924Z", "type": "info", "message": "Message search for 'requirements' not implemented or failed"}, {"timestamp": "2025-07-21T12:27:07.023Z", "type": "error", "message": "Request failed: GET /messages?search=timeline - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:07.023Z", "type": "info", "message": "Message search for 'timeline' not implemented or failed"}, {"timestamp": "2025-07-21T12:27:07.109Z", "type": "error", "message": "Request failed: GET /messages?since=2025-07-14T12:27:07.024Z - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:07.109Z", "type": "info", "message": "Date filtering not implemented yet"}, {"timestamp": "2025-07-21T12:27:07.110Z", "type": "success", "message": "Test PASSED: Message Search"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:07.110Z", "duration": 411}, {"name": "Conversation Management", "description": "Test conversation organization and management", "startTime": "2025-07-21T12:27:07.110Z", "logs": [{"timestamp": "2025-07-21T12:27:07.110Z", "type": "test", "message": "Starting test: Conversation Management - Test conversation organization and management"}, {"timestamp": "2025-07-21T12:27:07.808Z", "type": "error", "message": "Request failed: GET /messages/unread/count - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:07.808Z", "type": "info", "message": "Unread count endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:07.808Z", "type": "success", "message": "Test PASSED: Conversation Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:07.808Z", "duration": 698}]}, {"name": "Document Management", "icon": "📁", "total": 8, "passed": 2, "failed": 6, "successRate": "25.00", "tests": [{"name": "View Documents", "description": "Test viewing 30+ demo documents across file types", "startTime": "2025-07-21T12:27:08.384Z", "logs": [{"timestamp": "2025-07-21T12:27:08.384Z", "type": "test", "message": "Starting test: View Documents - Test viewing 30+ demo documents across file types"}, {"timestamp": "2025-07-21T12:27:08.510Z", "type": "error", "message": "Request failed: GET /documents - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:08.510Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:08.511Z", "type": "error", "message": "Test FAILED: View Documents"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:08.510Z", "duration": 126}, {"name": "Upload Document", "description": "Test uploading documents with metadata", "startTime": "2025-07-21T12:27:08.511Z", "logs": [{"timestamp": "2025-07-21T12:27:08.511Z", "type": "test", "message": "Starting test: Upload Document - Test uploading documents with metadata"}, {"timestamp": "2025-07-21T12:27:08.592Z", "type": "error", "message": "Request failed: POST /documents - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:27:08.593Z", "type": "info", "message": "Document upload endpoint may not be fully implemented yet"}, {"timestamp": "2025-07-21T12:27:08.593Z", "type": "success", "message": "Test PASSED: Upload Document"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:08.593Z", "duration": 82}, {"name": "Security Levels", "description": "Test document security levels and access control", "startTime": "2025-07-21T12:27:08.594Z", "logs": [{"timestamp": "2025-07-21T12:27:08.594Z", "type": "test", "message": "Starting test: Security Levels - Test document security levels and access control"}, {"timestamp": "2025-07-21T12:27:08.676Z", "type": "error", "message": "Request failed: GET /documents - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:08.677Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:08.677Z", "type": "error", "message": "Test FAILED: Security Levels"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:08.677Z", "duration": 83}, {"name": "Version Control", "description": "Test document version control and change tracking", "startTime": "2025-07-21T12:27:08.678Z", "logs": [{"timestamp": "2025-07-21T12:27:08.678Z", "type": "test", "message": "Starting test: Version Control - Test document version control and change tracking"}, {"timestamp": "2025-07-21T12:27:08.759Z", "type": "error", "message": "Request failed: GET /documents - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:08.760Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:08.761Z", "type": "error", "message": "Test FAILED: Version Control"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:08.760Z", "duration": 82}, {"name": "Role-based Access", "description": "Test role-based document access permissions", "startTime": "2025-07-21T12:27:08.762Z", "logs": [{"timestamp": "2025-07-21T12:27:08.762Z", "type": "test", "message": "Starting test: Role-based Access - Test role-based document access permissions"}, {"timestamp": "2025-07-21T12:27:08.969Z", "type": "error", "message": "Request failed: GET /documents - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:08.970Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:08.970Z", "type": "error", "message": "Test FAILED: Role-based Access"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:08.970Z", "duration": 208}, {"name": "Document Types", "description": "Test handling of different document file types", "startTime": "2025-07-21T12:27:08.971Z", "logs": [{"timestamp": "2025-07-21T12:27:08.971Z", "type": "test", "message": "Starting test: Document Types - Test handling of different document file types"}, {"timestamp": "2025-07-21T12:27:09.111Z", "type": "error", "message": "Request failed: GET /documents - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:09.112Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:09.112Z", "type": "error", "message": "Test FAILED: Document Types"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:09.112Z", "duration": 141}, {"name": "Document Search", "description": "Test document search and filtering capabilities", "startTime": "2025-07-21T12:27:09.113Z", "logs": [{"timestamp": "2025-07-21T12:27:09.113Z", "type": "test", "message": "Starting test: Document Search - Test document search and filtering capabilities"}, {"timestamp": "2025-07-21T12:27:09.240Z", "type": "error", "message": "Request failed: GET /documents?search=specification - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:09.241Z", "type": "info", "message": "Document search for 'specification' not implemented or failed"}, {"timestamp": "2025-07-21T12:27:09.321Z", "type": "error", "message": "Request failed: GET /documents?search=design - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:09.321Z", "type": "info", "message": "Document search for 'design' not implemented or failed"}, {"timestamp": "2025-07-21T12:27:09.444Z", "type": "error", "message": "Request failed: GET /documents?search=contract - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:09.445Z", "type": "info", "message": "Document search for 'contract' not implemented or failed"}, {"timestamp": "2025-07-21T12:27:09.524Z", "type": "error", "message": "Request failed: GET /documents?tags=requirements - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:09.525Z", "type": "info", "message": "Tag-based search not implemented yet"}, {"timestamp": "2025-07-21T12:27:09.605Z", "type": "error", "message": "Request failed: GET /documents?since=2025-06-21T12:27:09.525Z - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:09.606Z", "type": "info", "message": "Date filtering not implemented yet"}, {"timestamp": "2025-07-21T12:27:09.606Z", "type": "success", "message": "Test PASSED: Document Search"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:09.606Z", "duration": 493}, {"name": "Document Sharing", "description": "Test document sharing and collaboration features", "startTime": "2025-07-21T12:27:09.607Z", "logs": [{"timestamp": "2025-07-21T12:27:09.607Z", "type": "test", "message": "Starting test: Document Sharing - Test document sharing and collaboration features"}, {"timestamp": "2025-07-21T12:27:09.689Z", "type": "error", "message": "Request failed: GET /documents - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:09.690Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:09.691Z", "type": "error", "message": "Test FAILED: Document Sharing"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:09.690Z", "duration": 83}]}, {"name": "Analytics Dashboard", "icon": "📊", "total": 8, "passed": 4, "failed": 4, "successRate": "50.00", "tests": [{"name": "Dashboard Statistics", "description": "Test personalized dashboard statistics for different user types", "startTime": "2025-07-21T12:27:10.249Z", "logs": [{"timestamp": "2025-07-21T12:27:10.249Z", "type": "test", "message": "Starting test: Dashboard Statistics - Test personalized dashboard statistics for different user types"}, {"timestamp": "2025-07-21T12:27:10.552Z", "type": "info", "message": "Client dashboard data retrieved successfully"}, {"timestamp": "2025-07-21T12:27:10.553Z", "type": "info", "message": "Client dashboard fields: totalProjects, activeProjects, completedProjects, totalBids, role"}, {"timestamp": "2025-07-21T12:27:10.554Z", "type": "info", "message": "Missing client dashboard fields: totalSpent, averageProjectCost"}, {"timestamp": "2025-07-21T12:27:10.785Z", "type": "info", "message": "Vendor dashboard data retrieved successfully"}, {"timestamp": "2025-07-21T12:27:10.786Z", "type": "info", "message": "Vendor dashboard fields: totalBids, acceptedBids, pendingBids, activeProjects, role"}, {"timestamp": "2025-07-21T12:27:10.786Z", "type": "info", "message": "Missing vendor dashboard fields: totalEarnings, successRate"}, {"timestamp": "2025-07-21T12:27:10.869Z", "type": "info", "message": "Admin dashboard data retrieved successfully"}, {"timestamp": "2025-07-21T12:27:10.869Z", "type": "info", "message": "Admin dashboard fields: "}, {"timestamp": "2025-07-21T12:27:10.870Z", "type": "info", "message": "Missing admin dashboard fields: totalUsers, totalProjects, totalRevenue, platformGrowth"}, {"timestamp": "2025-07-21T12:27:10.871Z", "type": "success", "message": "Test PASSED: Dashboard Statistics"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:10.871Z", "duration": 622}, {"name": "Project Analytics", "description": "Test project performance analytics and insights", "startTime": "2025-07-21T12:27:10.871Z", "logs": [{"timestamp": "2025-07-21T12:27:10.871Z", "type": "test", "message": "Starting test: Project Analytics - Test project performance analytics and insights"}, {"timestamp": "2025-07-21T12:27:10.953Z", "type": "error", "message": "Request failed: GET /analytics/projects - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:10.954Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:10.954Z", "type": "error", "message": "Test FAILED: Project Analytics"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:10.954Z", "duration": 83}, {"name": "Revenue Tracking", "description": "Test revenue and earnings tracking functionality", "startTime": "2025-07-21T12:27:10.955Z", "logs": [{"timestamp": "2025-07-21T12:27:10.955Z", "type": "test", "message": "Starting test: Revenue Tracking - Test revenue and earnings tracking functionality"}, {"timestamp": "2025-07-21T12:27:11.037Z", "type": "error", "message": "Request failed: GET /analytics/revenue - Request failed with status code 403"}, {"timestamp": "2025-07-21T12:27:11.037Z", "type": "error", "message": "Test failed: Request failed with status code 403"}, {"timestamp": "2025-07-21T12:27:11.037Z", "type": "error", "message": "Test FAILED: Revenue Tracking"}], "passed": false, "errors": ["Request failed with status code 403"], "endTime": "2025-07-21T12:27:11.037Z", "duration": 82}, {"name": "User Engagement", "description": "Test user activity and engagement metrics", "startTime": "2025-07-21T12:27:11.038Z", "logs": [{"timestamp": "2025-07-21T12:27:11.038Z", "type": "test", "message": "Starting test: User Engagement - Test user activity and engagement metrics"}, {"timestamp": "2025-07-21T12:27:11.246Z", "type": "error", "message": "Request failed: GET /analytics/user/activity - Request failed with status code 500"}, {"timestamp": "2025-07-21T12:27:11.246Z", "type": "error", "message": "Test failed: Request failed with status code 500"}, {"timestamp": "2025-07-21T12:27:11.247Z", "type": "error", "message": "Test FAILED: User Engagement"}], "passed": false, "errors": ["Request failed with status code 500"], "endTime": "2025-07-21T12:27:11.246Z", "duration": 208}, {"name": "Performance Metrics", "description": "Test system and user performance metrics", "startTime": "2025-07-21T12:27:11.247Z", "logs": [{"timestamp": "2025-07-21T12:27:11.247Z", "type": "test", "message": "Starting test: Performance Metrics - Test system and user performance metrics"}, {"timestamp": "2025-07-21T12:27:11.330Z", "type": "error", "message": "Request failed: GET /analytics/system/performance - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:11.330Z", "type": "info", "message": "System performance metrics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:11.410Z", "type": "error", "message": "Request failed: GET /analytics/user/performance - Request failed with status code 500"}, {"timestamp": "2025-07-21T12:27:11.411Z", "type": "error", "message": "Test failed: Request failed with status code 500"}, {"timestamp": "2025-07-21T12:27:11.412Z", "type": "error", "message": "Test FAILED: Performance Metrics"}], "passed": false, "errors": ["Request failed with status code 500"], "endTime": "2025-07-21T12:27:11.411Z", "duration": 164}, {"name": "Analytics Filtering", "description": "Test filtering and date range functionality in analytics", "startTime": "2025-07-21T12:27:11.413Z", "logs": [{"timestamp": "2025-07-21T12:27:11.413Z", "type": "test", "message": "Starting test: Analytics Filtering - Test filtering and date range functionality in analytics"}, {"timestamp": "2025-07-21T12:27:11.678Z", "type": "info", "message": "Analytics for Last 7 days retrieved successfully"}, {"timestamp": "2025-07-21T12:27:11.991Z", "type": "info", "message": "Analytics for Last 30 days retrieved successfully"}, {"timestamp": "2025-07-21T12:27:12.262Z", "type": "info", "message": "Analytics for Last 3 months retrieved successfully"}, {"timestamp": "2025-07-21T12:27:12.613Z", "type": "info", "message": "Analytics for Last year retrieved successfully"}, {"timestamp": "2025-07-21T12:27:12.883Z", "type": "info", "message": "Custom date range analytics retrieved successfully"}, {"timestamp": "2025-07-21T12:27:12.966Z", "type": "error", "message": "Request failed: GET /analytics/projects?category=web-development - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:12.967Z", "type": "info", "message": "Category filtering not implemented yet"}, {"timestamp": "2025-07-21T12:27:13.057Z", "type": "error", "message": "Request failed: GET /analytics/projects?status=completed - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:13.058Z", "type": "info", "message": "Status filtering not implemented yet"}, {"timestamp": "2025-07-21T12:27:13.058Z", "type": "success", "message": "Test PASSED: Analytics Filtering"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:13.058Z", "duration": 1645}, {"name": "Data Export", "description": "Test analytics data export functionality", "startTime": "2025-07-21T12:27:13.059Z", "logs": [{"timestamp": "2025-07-21T12:27:13.059Z", "type": "test", "message": "Starting test: Data Export - Test analytics data export functionality"}, {"timestamp": "2025-07-21T12:27:13.139Z", "type": "error", "message": "Request failed: GET /analytics/export?format=csv&type=projects - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:13.141Z", "type": "info", "message": "CSV export not implemented yet"}, {"timestamp": "2025-07-21T12:27:13.280Z", "type": "error", "message": "Request failed: GET /analytics/export?format=json&type=revenue - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:13.280Z", "type": "info", "message": "JSON export not implemented yet"}, {"timestamp": "2025-07-21T12:27:13.362Z", "type": "error", "message": "Request failed: POST /analytics/reports/generate - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:13.363Z", "type": "info", "message": "PDF report generation not implemented yet"}, {"timestamp": "2025-07-21T12:27:13.364Z", "type": "success", "message": "Test PASSED: Data Export"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:13.364Z", "duration": 305}, {"name": "Real-time Updates", "description": "Test real-time analytics updates and live data", "startTime": "2025-07-21T12:27:13.364Z", "logs": [{"timestamp": "2025-07-21T12:27:13.364Z", "type": "test", "message": "Starting test: Real-time Updates - Test real-time analytics updates and live data"}, {"timestamp": "2025-07-21T12:27:13.447Z", "type": "error", "message": "Request failed: GET /analytics/realtime - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:13.447Z", "type": "info", "message": "Real-time analytics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:13.533Z", "type": "error", "message": "Request failed: GET /analytics/live/metrics - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:13.534Z", "type": "info", "message": "Live metrics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:13.640Z", "type": "error", "message": "Request failed: POST /analytics/refresh - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:13.641Z", "type": "info", "message": "Analytics refresh endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:13.641Z", "type": "success", "message": "Test PASSED: Real-time Updates"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:13.641Z", "duration": 277}]}, {"name": "Admin Panel", "icon": "👑", "total": 8, "passed": 5, "failed": 3, "successRate": "62.50", "tests": [{"name": "User Management", "description": "Test user management and administration features", "startTime": "2025-07-21T12:27:14.065Z", "logs": [{"timestamp": "2025-07-21T12:27:14.065Z", "type": "test", "message": "Starting test: User Management - Test user management and administration features"}, {"timestamp": "2025-07-21T12:27:14.068Z", "type": "error", "message": "Request failed: GET /admin/users - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.068Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.069Z", "type": "error", "message": "Test FAILED: User Management"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:14.068Z", "duration": 3}, {"name": "Role Assignment", "description": "Test user role management and permissions", "startTime": "2025-07-21T12:27:14.070Z", "logs": [{"timestamp": "2025-07-21T12:27:14.070Z", "type": "test", "message": "Starting test: Role Assignment - Test user role management and permissions"}, {"timestamp": "2025-07-21T12:27:14.072Z", "type": "error", "message": "Request failed: GET /admin/users - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.073Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.073Z", "type": "error", "message": "Test FAILED: Role Assignment"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:14.073Z", "duration": 3}, {"name": "Platform Analytics", "description": "Test platform-wide analytics and reporting", "startTime": "2025-07-21T12:27:14.074Z", "logs": [{"timestamp": "2025-07-21T12:27:14.074Z", "type": "test", "message": "Starting test: Platform Analytics - Test platform-wide analytics and reporting"}, {"timestamp": "2025-07-21T12:27:14.078Z", "type": "error", "message": "Request failed: GET /admin/analytics/overview - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.078Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.079Z", "type": "error", "message": "Test FAILED: Platform Analytics"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:27:14.078Z", "duration": 4}, {"name": "System Configuration", "description": "Test system settings and configuration management", "startTime": "2025-07-21T12:27:14.079Z", "logs": [{"timestamp": "2025-07-21T12:27:14.079Z", "type": "test", "message": "Starting test: System Configuration - Test system settings and configuration management"}, {"timestamp": "2025-07-21T12:27:14.082Z", "type": "error", "message": "Request failed: GET /admin/config - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.083Z", "type": "info", "message": "System configuration endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.087Z", "type": "error", "message": "Request failed: PUT /admin/config - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.088Z", "type": "info", "message": "System configuration update endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.093Z", "type": "error", "message": "Request failed: GET /admin/features - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.093Z", "type": "info", "message": "Feature flags endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.096Z", "type": "error", "message": "Request failed: PUT /admin/features - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.097Z", "type": "info", "message": "Feature flag update endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.101Z", "type": "error", "message": "Request failed: GET /admin/limits - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.102Z", "type": "info", "message": "System limits endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.102Z", "type": "success", "message": "Test PASSED: System Configuration"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:14.102Z", "duration": 23}, {"name": "Content Moderation", "description": "Test content moderation and security features", "startTime": "2025-07-21T12:27:14.103Z", "logs": [{"timestamp": "2025-07-21T12:27:14.103Z", "type": "test", "message": "Starting test: Content Moderation - Test content moderation and security features"}, {"timestamp": "2025-07-21T12:27:14.108Z", "type": "error", "message": "Request failed: GET /admin/moderation/flagged - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.109Z", "type": "info", "message": "Flagged content endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.113Z", "type": "error", "message": "Request failed: GET /admin/moderation/flagged - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.113Z", "type": "info", "message": "Content approval endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.116Z", "type": "error", "message": "Request failed: GET /admin/reports/users - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.116Z", "type": "info", "message": "User reports endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.122Z", "type": "error", "message": "Request failed: POST /admin/moderation/scan - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.122Z", "type": "info", "message": "Content scanning endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.126Z", "type": "error", "message": "Request failed: GET /admin/moderation/rules - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.126Z", "type": "info", "message": "Content filtering rules endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.127Z", "type": "success", "message": "Test PASSED: Content Moderation"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:14.127Z", "duration": 24}, {"name": "Security Management", "description": "Test security monitoring and management features", "startTime": "2025-07-21T12:27:14.127Z", "logs": [{"timestamp": "2025-07-21T12:27:14.127Z", "type": "test", "message": "Starting test: Security Management - Test security monitoring and management features"}, {"timestamp": "2025-07-21T12:27:14.130Z", "type": "error", "message": "Request failed: GET /admin/security/logs - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.131Z", "type": "info", "message": "Security logs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.135Z", "type": "error", "message": "Request failed: GET /admin/security/failed-logins - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.135Z", "type": "info", "message": "Failed logins endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.140Z", "type": "error", "message": "Request failed: GET /admin/security/blocked-ips - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.141Z", "type": "info", "message": "Blocked IPs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.145Z", "type": "error", "message": "Request failed: GET /admin/security/alerts - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.145Z", "type": "info", "message": "Security alerts endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.146Z", "type": "success", "message": "Test PASSED: Security Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:14.146Z", "duration": 19}, {"name": "System Monitoring", "description": "Test system health and performance monitoring", "startTime": "2025-07-21T12:27:14.147Z", "logs": [{"timestamp": "2025-07-21T12:27:14.147Z", "type": "test", "message": "Starting test: System Monitoring - Test system health and performance monitoring"}, {"timestamp": "2025-07-21T12:27:14.151Z", "type": "error", "message": "Request failed: GET /admin/system/health - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.151Z", "type": "info", "message": "System health endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.156Z", "type": "error", "message": "Request failed: GET /admin/system/performance - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.157Z", "type": "info", "message": "Performance metrics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.162Z", "type": "error", "message": "Request failed: GET /admin/system/errors - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.163Z", "type": "info", "message": "Error logs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.167Z", "type": "error", "message": "Request failed: GET /admin/system/status - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.168Z", "type": "info", "message": "System status endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.168Z", "type": "success", "message": "Test PASSED: System Monitoring"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:14.168Z", "duration": 21}, {"name": "Data Management", "description": "Test data backup, export, and maintenance features", "startTime": "2025-07-21T12:27:14.169Z", "logs": [{"timestamp": "2025-07-21T12:27:14.169Z", "type": "test", "message": "Starting test: Data Management - Test data backup, export, and maintenance features"}, {"timestamp": "2025-07-21T12:27:14.172Z", "type": "error", "message": "Request failed: POST /admin/data/export - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.173Z", "type": "info", "message": "Data export endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.179Z", "type": "error", "message": "Request failed: GET /admin/data/backups - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.179Z", "type": "info", "message": "Backup status endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.184Z", "type": "error", "message": "Request failed: POST /admin/data/cleanup - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.184Z", "type": "info", "message": "Data cleanup endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.187Z", "type": "error", "message": "Request failed: GET /admin/data/statistics - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:27:14.188Z", "type": "info", "message": "Database statistics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:27:14.189Z", "type": "success", "message": "Test PASSED: Data Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:27:14.189Z", "duration": 20}]}], "generatedAt": "2025-07-21T12:27:14.196Z"}