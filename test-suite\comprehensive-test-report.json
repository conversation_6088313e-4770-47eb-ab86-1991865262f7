{"summary": {"startTime": "2025-07-21T12:42:09.482Z", "endTime": "2025-07-21T12:42:31.973Z", "duration": 22491, "totalSuites": 6, "totalTests": 48, "totalPassed": 33, "totalFailed": 15, "overallSuccessRate": "68.75"}, "suites": [{"name": "Project Management", "icon": "📋", "total": 8, "passed": 5, "failed": 3, "successRate": "62.50", "tests": [{"name": "Browse Projects", "description": "Test browsing 8+ demo projects across different categories", "startTime": "2025-07-21T12:42:10.097Z", "logs": [{"timestamp": "2025-07-21T12:42:10.097Z", "type": "test", "message": "Starting test: Browse Projects - Test browsing 8+ demo projects across different categories"}, {"timestamp": "2025-07-21T12:42:10.253Z", "type": "info", "message": "Found 7 projects for client"}, {"timestamp": "2025-07-21T12:42:10.253Z", "type": "error", "message": "Test failed: Expected at least 8 projects, found 7"}, {"timestamp": "2025-07-21T12:42:10.254Z", "type": "error", "message": "Test FAILED: Browse Projects"}], "passed": false, "errors": ["Expected at least 8 projects, found 7"], "endTime": "2025-07-21T12:42:10.253Z", "duration": 156}, {"name": "Create Project", "description": "Test creating new projects with budget and timeline", "startTime": "2025-07-21T12:42:10.254Z", "logs": [{"timestamp": "2025-07-21T12:42:10.254Z", "type": "test", "message": "Starting test: Create Project - Test creating new projects with budget and timeline"}, {"timestamp": "2025-07-21T12:42:10.305Z", "type": "error", "message": "Request failed: POST /projects - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:42:10.306Z", "type": "error", "message": "Test failed: Request failed with status code 400"}, {"timestamp": "2025-07-21T12:42:10.307Z", "type": "error", "message": "Test FAILED: Create Project"}], "passed": false, "errors": ["Request failed with status code 400"], "endTime": "2025-07-21T12:42:10.306Z", "duration": 52}, {"name": "Project Details", "description": "Test viewing detailed project information", "startTime": "2025-07-21T12:42:10.307Z", "logs": [{"timestamp": "2025-07-21T12:42:10.307Z", "type": "test", "message": "Starting test: Project Details - Test viewing detailed project information"}, {"timestamp": "2025-07-21T12:42:10.595Z", "type": "info", "message": "Project details loaded for: E-commerce Website Development"}, {"timestamp": "2025-07-21T12:42:10.719Z", "type": "info", "message": "Project details accessible to vendors"}, {"timestamp": "2025-07-21T12:42:10.719Z", "type": "success", "message": "Test PASSED: Project Details"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:10.719Z", "duration": 412}, {"name": "Update Project Status", "description": "Test managing project status and lifecycle", "startTime": "2025-07-21T12:42:10.720Z", "logs": [{"timestamp": "2025-07-21T12:42:10.720Z", "type": "test", "message": "Starting test: Update Project Status - Test managing project status and lifecycle"}, {"timestamp": "2025-07-21T12:42:10.978Z", "type": "error", "message": "Test failed: Project status was not updated"}, {"timestamp": "2025-07-21T12:42:10.978Z", "type": "error", "message": "Test FAILED: Update Project Status"}], "passed": false, "errors": ["Project status was not updated"], "endTime": "2025-07-21T12:42:10.978Z", "duration": 258}, {"name": "Project Milestones", "description": "Test project milestone management", "startTime": "2025-07-21T12:42:10.979Z", "logs": [{"timestamp": "2025-07-21T12:42:10.979Z", "type": "test", "message": "Starting test: Project Milestones - Test project milestone management"}, {"timestamp": "2025-07-21T12:42:11.176Z", "type": "error", "message": "Request failed: POST /projects/687e3205b817f2dcbff7a883/milestones - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:11.176Z", "type": "info", "message": "Milestone endpoint not implemented yet - this is expected"}, {"timestamp": "2025-07-21T12:42:11.222Z", "type": "error", "message": "Request failed: GET /projects/687e3205b817f2dcbff7a883/milestones - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:11.223Z", "type": "info", "message": "Milestone listing endpoint not implemented yet - this is expected"}, {"timestamp": "2025-07-21T12:42:11.224Z", "type": "success", "message": "Test PASSED: Project Milestones"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:11.224Z", "duration": 245}, {"name": "Project Analytics", "description": "Test project performance analytics", "startTime": "2025-07-21T12:42:11.225Z", "logs": [{"timestamp": "2025-07-21T12:42:11.225Z", "type": "test", "message": "Starting test: Project Analytics - Test project performance analytics"}, {"timestamp": "2025-07-21T12:42:11.395Z", "type": "info", "message": "Project analytics data retrieved"}, {"timestamp": "2025-07-21T12:42:11.395Z", "type": "info", "message": "Analytics fields available: total, byStatus, byCategory, projects"}, {"timestamp": "2025-07-21T12:42:11.571Z", "type": "error", "message": "Request failed: GET /projects/687e3205b817f2dcbff7a883/analytics - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:11.571Z", "type": "info", "message": "Individual project analytics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:11.572Z", "type": "success", "message": "Test PASSED: Project Analytics"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:11.572Z", "duration": 347}, {"name": "Project Categories", "description": "Test project categorization and filtering", "startTime": "2025-07-21T12:42:11.572Z", "logs": [{"timestamp": "2025-07-21T12:42:11.572Z", "type": "test", "message": "Starting test: Project Categories - Test project categorization and filtering"}, {"timestamp": "2025-07-21T12:42:11.699Z", "type": "info", "message": "Available categories: web-development, design, marketing, mobile-development, data-analytics, content"}, {"timestamp": "2025-07-21T12:42:11.866Z", "type": "info", "message": "Category filter 'web-development' working correctly (1 projects)"}, {"timestamp": "2025-07-21T12:42:11.994Z", "type": "info", "message": "Category filter 'design' working correctly (2 projects)"}, {"timestamp": "2025-07-21T12:42:11.995Z", "type": "success", "message": "Test PASSED: Project Categories"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:11.995Z", "duration": 423}, {"name": "Project Search", "description": "Test project search functionality", "startTime": "2025-07-21T12:42:11.996Z", "logs": [{"timestamp": "2025-07-21T12:42:11.996Z", "type": "test", "message": "Starting test: Project Search - Test project search functionality"}, {"timestamp": "2025-07-21T12:42:12.120Z", "type": "info", "message": "Search for 'web' returned 7 results"}, {"timestamp": "2025-07-21T12:42:12.120Z", "type": "info", "message": "2 results are relevant to search term 'web'"}, {"timestamp": "2025-07-21T12:42:12.244Z", "type": "info", "message": "Search for 'mobile' returned 7 results"}, {"timestamp": "2025-07-21T12:42:12.245Z", "type": "info", "message": "3 results are relevant to search term 'mobile'"}, {"timestamp": "2025-07-21T12:42:12.402Z", "type": "info", "message": "Search for 'design' returned 7 results"}, {"timestamp": "2025-07-21T12:42:12.402Z", "type": "info", "message": "2 results are relevant to search term 'design'"}, {"timestamp": "2025-07-21T12:42:12.403Z", "type": "success", "message": "Test PASSED: Project Search"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:12.403Z", "duration": 407}]}, {"name": "Bidding System", "icon": "💰", "total": 8, "passed": 4, "failed": 4, "successRate": "50.00", "tests": [{"name": "View Bids", "description": "Test viewing 20+ realistic bids on various projects", "startTime": "2025-07-21T12:42:13.078Z", "logs": [{"timestamp": "2025-07-21T12:42:13.078Z", "type": "test", "message": "Starting test: View Bids - Test viewing 20+ realistic bids on various projects"}, {"timestamp": "2025-07-21T12:42:13.393Z", "type": "info", "message": "Found 9 total bids in system"}, {"timestamp": "2025-07-21T12:42:13.394Z", "type": "info", "message": "Warning: Expected at least 20 bids, found 9"}, {"timestamp": "2025-07-21T12:42:13.395Z", "type": "info", "message": "Bid structure validation passed"}, {"timestamp": "2025-07-21T12:42:13.563Z", "type": "error", "message": "Request failed: GET /projects/687e3205b817f2dcbff7a883/bids - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:13.563Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:13.564Z", "type": "error", "message": "Test FAILED: View Bids"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:42:13.563Z", "duration": 485}, {"name": "Submit Bid", "description": "Test submitting detailed proposals", "startTime": "2025-07-21T12:42:13.565Z", "logs": [{"timestamp": "2025-07-21T12:42:13.565Z", "type": "test", "message": "Starting test: Submit Bid - Test submitting detailed proposals"}, {"timestamp": "2025-07-21T12:42:13.845Z", "type": "error", "message": "Request failed: POST /bids - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:42:13.845Z", "type": "error", "message": "Test failed: Request failed with status code 400"}, {"timestamp": "2025-07-21T12:42:13.846Z", "type": "error", "message": "Test FAILED: Submit Bid"}], "passed": false, "errors": ["Request failed with status code 400"], "endTime": "2025-07-21T12:42:13.845Z", "duration": 280}, {"name": "Bid with Attachments", "description": "Test submitting proposals with file attachments", "startTime": "2025-07-21T12:42:13.847Z", "logs": [{"timestamp": "2025-07-21T12:42:13.847Z", "type": "test", "message": "Starting test: Bid with Attachments - Test submitting proposals with file attachments"}, {"timestamp": "2025-07-21T12:42:14.091Z", "type": "error", "message": "Request failed: POST /bids - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:42:14.091Z", "type": "info", "message": "Attachment functionality may not be fully implemented yet"}, {"timestamp": "2025-07-21T12:42:14.092Z", "type": "success", "message": "Test PASSED: Bid with Attachments"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:14.092Z", "duration": 245}, {"name": "Accept Bid", "description": "Test accepting bids as a client", "startTime": "2025-07-21T12:42:14.092Z", "logs": [{"timestamp": "2025-07-21T12:42:14.092Z", "type": "test", "message": "Starting test: Accept Bid - Test accepting bids as a client"}, {"timestamp": "2025-07-21T12:42:14.300Z", "type": "error", "message": "Request failed: GET /projects/687e3205b817f2dcbff7a883/bids - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:14.301Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:14.301Z", "type": "error", "message": "Test FAILED: Accept Bid"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:42:14.301Z", "duration": 209}, {"name": "Reject Bid", "description": "Test rejecting bids as a client", "startTime": "2025-07-21T12:42:14.302Z", "logs": [{"timestamp": "2025-07-21T12:42:14.302Z", "type": "test", "message": "Starting test: Reject Bid - Test rejecting bids as a client"}, {"timestamp": "2025-07-21T12:42:14.507Z", "type": "error", "message": "Request failed: GET /projects/687e3205b817f2dcbff7a883/bids - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:14.507Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:14.508Z", "type": "error", "message": "Test FAILED: Reject Bid"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:42:14.507Z", "duration": 205}, {"name": "Bid Status Tracking", "description": "Test tracking bid status and changes", "startTime": "2025-07-21T12:42:14.509Z", "logs": [{"timestamp": "2025-07-21T12:42:14.509Z", "type": "test", "message": "Starting test: Bid Status Tracking - Test tracking bid status and changes"}, {"timestamp": "2025-07-21T12:42:14.713Z", "type": "info", "message": "Bid status distribution: {\"pending\":5,\"accepted\":1}"}, {"timestamp": "2025-07-21T12:42:14.914Z", "type": "info", "message": "Status filter 'pending' working correctly (5 bids)"}, {"timestamp": "2025-07-21T12:42:15.155Z", "type": "info", "message": "Status filter 'accepted' working correctly (1 bids)"}, {"timestamp": "2025-07-21T12:42:15.155Z", "type": "success", "message": "Test PASSED: Bid Status Tracking"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:15.155Z", "duration": 646}, {"name": "Bid Negotiation", "description": "Test bid negotiation and communication", "startTime": "2025-07-21T12:42:15.156Z", "logs": [{"timestamp": "2025-07-21T12:42:15.156Z", "type": "test", "message": "Starting test: Bid Negotiation - Test bid negotiation and communication"}, {"timestamp": "2025-07-21T12:42:15.486Z", "type": "error", "message": "Request failed: POST /bids/687e3205b817f2dcbff7a89b/negotiate - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:15.486Z", "type": "info", "message": "Bid negotiation endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:15.570Z", "type": "error", "message": "Request failed: GET /bids/687e3205b817f2dcbff7a89b/negotiations - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:15.571Z", "type": "info", "message": "Negotiation history endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:15.572Z", "type": "success", "message": "Test PASSED: Bid Negotiation"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:15.572Z", "duration": 416}, {"name": "<PERSON><PERSON><PERSON>", "description": "Test vendor bid history and analytics", "startTime": "2025-07-21T12:42:15.572Z", "logs": [{"timestamp": "2025-07-21T12:42:15.572Z", "type": "test", "message": "Starting test: Vendor Bid History - Test vendor bid history and analytics"}, {"timestamp": "2025-07-21T12:42:15.777Z", "type": "info", "message": "Vendor has 6 total bids"}, {"timestamp": "2025-07-21T12:42:15.778Z", "type": "info", "message": "Bid statistics - Accepted: 1, Rejected: 0, Pending: 5"}, {"timestamp": "2025-07-21T12:42:15.779Z", "type": "info", "message": "Success rate: 16.67%"}, {"timestamp": "2025-07-21T12:42:15.863Z", "type": "error", "message": "Request failed: GET /analytics/bids - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:15.864Z", "type": "info", "message": "Bid analytics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:15.864Z", "type": "success", "message": "Test PASSED: <PERSON><PERSON><PERSON>"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:15.864Z", "duration": 292}]}, {"name": "Messaging System", "icon": "💬", "total": 8, "passed": 5, "failed": 3, "successRate": "62.50", "tests": [{"name": "View Messages", "description": "Test viewing existing messages and conversations", "startTime": "2025-07-21T12:42:16.412Z", "logs": [{"timestamp": "2025-07-21T12:42:16.412Z", "type": "test", "message": "Starting test: View Messages - Test viewing existing messages and conversations"}, {"timestamp": "2025-07-21T12:42:16.727Z", "type": "info", "message": "Found 16 messages for client"}, {"timestamp": "2025-07-21T12:42:16.727Z", "type": "error", "message": "Test failed: Message missing required field: recipient"}, {"timestamp": "2025-07-21T12:42:16.728Z", "type": "error", "message": "Test FAILED: View Messages"}], "passed": false, "errors": ["Message missing required field: recipient"], "endTime": "2025-07-21T12:42:16.727Z", "duration": 315}, {"name": "Send Message", "description": "Test sending messages between users", "startTime": "2025-07-21T12:42:16.728Z", "logs": [{"timestamp": "2025-07-21T12:42:16.728Z", "type": "test", "message": "Starting test: Send Message - Test sending messages between users"}, {"timestamp": "2025-07-21T12:42:16.816Z", "type": "error", "message": "Request failed: POST /messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:16.817Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:16.818Z", "type": "error", "message": "Test FAILED: Send Message"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:42:16.817Z", "duration": 89}, {"name": "Message with Attachments", "description": "Test sending messages with file attachments", "startTime": "2025-07-21T12:42:16.818Z", "logs": [{"timestamp": "2025-07-21T12:42:16.818Z", "type": "test", "message": "Starting test: Message with At<PERSON>chments - Test sending messages with file attachments"}, {"timestamp": "2025-07-21T12:42:16.905Z", "type": "error", "message": "Request failed: POST /messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:16.906Z", "type": "info", "message": "Message attachment functionality may not be fully implemented yet"}, {"timestamp": "2025-07-21T12:42:16.906Z", "type": "success", "message": "Test PASSED: Message with Attachments"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:16.906Z", "duration": 88}, {"name": "Project Conversations", "description": "Test project-specific messaging", "startTime": "2025-07-21T12:42:16.907Z", "logs": [{"timestamp": "2025-07-21T12:42:16.907Z", "type": "test", "message": "Starting test: Project Conversations - Test project-specific messaging"}, {"timestamp": "2025-07-21T12:42:17.134Z", "type": "error", "message": "Request failed: POST /messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:17.134Z", "type": "info", "message": "Project-specific messaging may not be fully implemented"}, {"timestamp": "2025-07-21T12:42:17.178Z", "type": "error", "message": "Request failed: GET /projects/687e3205b817f2dcbff7a883/messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:17.179Z", "type": "info", "message": "Project message listing endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:17.180Z", "type": "success", "message": "Test PASSED: Project Conversations"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:17.180Z", "duration": 273}, {"name": "Group Conversations", "description": "Test group messaging for team collaboration", "startTime": "2025-07-21T12:42:17.182Z", "logs": [{"timestamp": "2025-07-21T12:42:17.182Z", "type": "test", "message": "Starting test: Group Conversations - Test group messaging for team collaboration"}, {"timestamp": "2025-07-21T12:42:17.303Z", "type": "error", "message": "Request failed: POST /messages/groups - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:17.303Z", "type": "info", "message": "Group conversation functionality not implemented yet"}, {"timestamp": "2025-07-21T12:42:17.387Z", "type": "error", "message": "Request failed: GET /messages/groups - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:17.388Z", "type": "info", "message": "Group listing endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:17.388Z", "type": "success", "message": "Test PASSED: Group Conversations"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:17.388Z", "duration": 206}, {"name": "Message Delivery", "description": "Test real-time message delivery and read receipts", "startTime": "2025-07-21T12:42:17.389Z", "logs": [{"timestamp": "2025-07-21T12:42:17.389Z", "type": "test", "message": "Starting test: Message Delivery - Test real-time message delivery and read receipts"}, {"timestamp": "2025-07-21T12:42:17.472Z", "type": "error", "message": "Request failed: POST /messages - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:17.473Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:17.474Z", "type": "error", "message": "Test FAILED: Message Delivery"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:42:17.473Z", "duration": 84}, {"name": "Message Search", "description": "Test searching messages by content and metadata", "startTime": "2025-07-21T12:42:17.475Z", "logs": [{"timestamp": "2025-07-21T12:42:17.475Z", "type": "test", "message": "Starting test: Message Search - Test searching messages by content and metadata"}, {"timestamp": "2025-07-21T12:42:17.720Z", "type": "info", "message": "Search for 'project' returned 3 results"}, {"timestamp": "2025-07-21T12:42:17.720Z", "type": "info", "message": "3 results are relevant to search term 'project'"}, {"timestamp": "2025-07-21T12:42:17.999Z", "type": "info", "message": "Search for 'requirements' returned 3 results"}, {"timestamp": "2025-07-21T12:42:18.000Z", "type": "info", "message": "3 results are relevant to search term 'requirements'"}, {"timestamp": "2025-07-21T12:42:18.250Z", "type": "info", "message": "Search for 'timeline' returned 2 results"}, {"timestamp": "2025-07-21T12:42:18.251Z", "type": "info", "message": "2 results are relevant to search term 'timeline'"}, {"timestamp": "2025-07-21T12:42:18.612Z", "type": "info", "message": "Found 16 messages from the last week"}, {"timestamp": "2025-07-21T12:42:18.613Z", "type": "success", "message": "Test PASSED: Message Search"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:18.613Z", "duration": 1138}, {"name": "Conversation Management", "description": "Test conversation organization and management", "startTime": "2025-07-21T12:42:18.614Z", "logs": [{"timestamp": "2025-07-21T12:42:18.614Z", "type": "test", "message": "Starting test: Conversation Management - Test conversation organization and management"}, {"timestamp": "2025-07-21T12:42:19.230Z", "type": "error", "message": "Request failed: GET /messages/unread/count - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:19.230Z", "type": "info", "message": "Unread count endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:19.231Z", "type": "success", "message": "Test PASSED: Conversation Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:19.231Z", "duration": 617}]}, {"name": "Document Management", "icon": "📁", "total": 8, "passed": 7, "failed": 1, "successRate": "87.50", "tests": [{"name": "View Documents", "description": "Test viewing 30+ demo documents across file types", "startTime": "2025-07-21T12:42:19.787Z", "logs": [{"timestamp": "2025-07-21T12:42:19.787Z", "type": "test", "message": "Starting test: View Documents - Test viewing 30+ demo documents across file types"}, {"timestamp": "2025-07-21T12:42:20.027Z", "type": "info", "message": "Found 10 total documents in system"}, {"timestamp": "2025-07-21T12:42:20.028Z", "type": "info", "message": "Warning: Expected at least 30 documents, found 10"}, {"timestamp": "2025-07-21T12:42:20.029Z", "type": "error", "message": "Test failed: Document missing required field: name"}, {"timestamp": "2025-07-21T12:42:20.030Z", "type": "error", "message": "Test FAILED: View Documents"}], "passed": false, "errors": ["Document missing required field: name"], "endTime": "2025-07-21T12:42:20.029Z", "duration": 242}, {"name": "Upload Document", "description": "Test uploading documents with metadata", "startTime": "2025-07-21T12:42:20.030Z", "logs": [{"timestamp": "2025-07-21T12:42:20.030Z", "type": "test", "message": "Starting test: Upload Document - Test uploading documents with metadata"}, {"timestamp": "2025-07-21T12:42:20.114Z", "type": "error", "message": "Request failed: POST /documents - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:42:20.115Z", "type": "info", "message": "Document upload endpoint may not be fully implemented yet"}, {"timestamp": "2025-07-21T12:42:20.116Z", "type": "success", "message": "Test PASSED: Upload Document"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:20.116Z", "duration": 86}, {"name": "Security Levels", "description": "Test document security levels and access control", "startTime": "2025-07-21T12:42:20.116Z", "logs": [{"timestamp": "2025-07-21T12:42:20.116Z", "type": "test", "message": "Starting test: Security Levels - Test document security levels and access control"}, {"timestamp": "2025-07-21T12:42:20.464Z", "type": "info", "message": "Security level distribution: {\"1\":4,\"2\":5,\"3\":1}"}, {"timestamp": "2025-07-21T12:42:20.551Z", "type": "error", "message": "Request failed: POST /documents - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:42:20.551Z", "type": "info", "message": "Security level public creation failed or not implemented"}, {"timestamp": "2025-07-21T12:42:20.635Z", "type": "error", "message": "Request failed: POST /documents - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:42:20.635Z", "type": "info", "message": "Security level private creation failed or not implemented"}, {"timestamp": "2025-07-21T12:42:20.721Z", "type": "error", "message": "Request failed: POST /documents - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:42:20.721Z", "type": "info", "message": "Security level confidential creation failed or not implemented"}, {"timestamp": "2025-07-21T12:42:20.722Z", "type": "success", "message": "Test PASSED: Security Levels"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:20.722Z", "duration": 606}, {"name": "Version Control", "description": "Test document version control and change tracking", "startTime": "2025-07-21T12:42:20.722Z", "logs": [{"timestamp": "2025-07-21T12:42:20.722Z", "type": "test", "message": "Starting test: Version Control - Test document version control and change tracking"}, {"timestamp": "2025-07-21T12:42:21.090Z", "type": "error", "message": "Request failed: POST /documents/687e3207b817f2dcbff7a923/versions - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:21.091Z", "type": "info", "message": "Document versioning endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:21.177Z", "type": "error", "message": "Request failed: GET /documents/687e3207b817f2dcbff7a923/versions - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:21.178Z", "type": "info", "message": "Version history endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:21.264Z", "type": "error", "message": "Request failed: GET /documents/687e3207b817f2dcbff7a923/versions - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:21.264Z", "type": "info", "message": "Version comparison endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:21.265Z", "type": "success", "message": "Test PASSED: Version Control"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:21.265Z", "duration": 543}, {"name": "Role-based Access", "description": "Test role-based document access permissions", "startTime": "2025-07-21T12:42:21.266Z", "logs": [{"timestamp": "2025-07-21T12:42:21.266Z", "type": "test", "message": "Starting test: Role-based Access - Test role-based document access permissions"}, {"timestamp": "2025-07-21T12:42:21.479Z", "type": "info", "message": "<PERSON><PERSON> can see 20 documents"}, {"timestamp": "2025-07-21T12:42:21.778Z", "type": "info", "message": "Client can see 10 documents"}, {"timestamp": "2025-07-21T12:42:22.036Z", "type": "info", "message": "<PERSON><PERSON><PERSON> can see 10 documents"}, {"timestamp": "2025-07-21T12:42:22.154Z", "type": "error", "message": "Request failed: GET /documents/687e3207b817f2dcbff7a923/permissions - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:22.154Z", "type": "info", "message": "Document permissions endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:22.237Z", "type": "error", "message": "Request failed: PUT /documents/687e3207b817f2dcbff7a923/permissions - Request failed with status code 400"}, {"timestamp": "2025-07-21T12:42:22.238Z", "type": "info", "message": "Permission update endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:22.435Z", "type": "error", "message": "Request failed: GET /projects/687e3205b817f2dcbff7a883/documents - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:22.435Z", "type": "info", "message": "Project document access endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:22.436Z", "type": "success", "message": "Test PASSED: Role-based Access"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:22.436Z", "duration": 1170}, {"name": "Document Types", "description": "Test handling of different document file types", "startTime": "2025-07-21T12:42:22.437Z", "logs": [{"timestamp": "2025-07-21T12:42:22.437Z", "type": "test", "message": "Starting test: Document Types - Test handling of different document file types"}, {"timestamp": "2025-07-21T12:42:22.691Z", "type": "info", "message": "File type analysis: {\n  \"unknown\": 10\n}"}, {"timestamp": "2025-07-21T12:42:22.692Z", "type": "info", "message": "Missing file types: application/pdf, image/png, image/jpeg, text/plain, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/zip"}, {"timestamp": "2025-07-21T12:42:22.936Z", "type": "info", "message": "Type filtering for 'unknown' not implemented or failed"}, {"timestamp": "2025-07-21T12:42:22.937Z", "type": "success", "message": "Test PASSED: Document Types"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:22.937Z", "duration": 500}, {"name": "Document Search", "description": "Test document search and filtering capabilities", "startTime": "2025-07-21T12:42:22.938Z", "logs": [{"timestamp": "2025-07-21T12:42:22.938Z", "type": "test", "message": "Starting test: Document Search - Test document search and filtering capabilities"}, {"timestamp": "2025-07-21T12:42:23.210Z", "type": "info", "message": "Search for 'specification' returned 5 results"}, {"timestamp": "2025-07-21T12:42:23.211Z", "type": "info", "message": "Document search for 'specification' not implemented or failed"}, {"timestamp": "2025-07-21T12:42:23.459Z", "type": "info", "message": "Search for 'design' returned 4 results"}, {"timestamp": "2025-07-21T12:42:23.459Z", "type": "info", "message": "Document search for 'design' not implemented or failed"}, {"timestamp": "2025-07-21T12:42:23.716Z", "type": "info", "message": "Search for 'contract' returned 0 results"}, {"timestamp": "2025-07-21T12:42:23.716Z", "type": "info", "message": "0 results are relevant to search term 'contract'"}, {"timestamp": "2025-07-21T12:42:23.954Z", "type": "info", "message": "Found 0 documents with 'requirements' tag"}, {"timestamp": "2025-07-21T12:42:24.206Z", "type": "info", "message": "Found 10 documents from the last month"}, {"timestamp": "2025-07-21T12:42:24.207Z", "type": "success", "message": "Test PASSED: Document Search"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:24.207Z", "duration": 1269}, {"name": "Document Sharing", "description": "Test document sharing and collaboration features", "startTime": "2025-07-21T12:42:24.208Z", "logs": [{"timestamp": "2025-07-21T12:42:24.208Z", "type": "test", "message": "Starting test: Document Sharing - Test document sharing and collaboration features"}, {"timestamp": "2025-07-21T12:42:24.574Z", "type": "error", "message": "Request failed: POST /documents/687e3207b817f2dcbff7a923/share - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:24.575Z", "type": "info", "message": "Document sharing endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:24.658Z", "type": "error", "message": "Request failed: POST /documents/687e3207b817f2dcbff7a923/share-link - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:24.658Z", "type": "info", "message": "Shareable link generation not implemented yet"}, {"timestamp": "2025-07-21T12:42:24.768Z", "type": "error", "message": "Request failed: POST /documents/687e3207b817f2dcbff7a923/comments - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:24.768Z", "type": "info", "message": "Document commenting not implemented yet"}, {"timestamp": "2025-07-21T12:42:24.769Z", "type": "success", "message": "Test PASSED: Document Sharing"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:24.769Z", "duration": 561}]}, {"name": "Analytics Dashboard", "icon": "📊", "total": 8, "passed": 5, "failed": 3, "successRate": "62.50", "tests": [{"name": "Dashboard Statistics", "description": "Test personalized dashboard statistics for different user types", "startTime": "2025-07-21T12:42:25.315Z", "logs": [{"timestamp": "2025-07-21T12:42:25.315Z", "type": "test", "message": "Starting test: Dashboard Statistics - Test personalized dashboard statistics for different user types"}, {"timestamp": "2025-07-21T12:42:25.594Z", "type": "info", "message": "Client dashboard data retrieved successfully"}, {"timestamp": "2025-07-21T12:42:25.594Z", "type": "info", "message": "Client dashboard fields: totalProjects, activeProjects, completedProjects, totalBids, role"}, {"timestamp": "2025-07-21T12:42:25.595Z", "type": "info", "message": "Missing client dashboard fields: totalSpent, averageProjectCost"}, {"timestamp": "2025-07-21T12:42:25.882Z", "type": "info", "message": "Vendor dashboard data retrieved successfully"}, {"timestamp": "2025-07-21T12:42:25.883Z", "type": "info", "message": "Vendor dashboard fields: totalBids, acceptedBids, pendingBids, activeProjects, role"}, {"timestamp": "2025-07-21T12:42:25.884Z", "type": "info", "message": "Missing vendor dashboard fields: totalEarnings, successRate"}, {"timestamp": "2025-07-21T12:42:25.970Z", "type": "info", "message": "Admin dashboard data retrieved successfully"}, {"timestamp": "2025-07-21T12:42:25.970Z", "type": "info", "message": "Admin dashboard fields: "}, {"timestamp": "2025-07-21T12:42:25.971Z", "type": "info", "message": "Missing admin dashboard fields: totalUsers, totalProjects, totalRevenue, platformGrowth"}, {"timestamp": "2025-07-21T12:42:25.971Z", "type": "success", "message": "Test PASSED: Dashboard Statistics"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:25.971Z", "duration": 656}, {"name": "Project Analytics", "description": "Test project performance analytics and insights", "startTime": "2025-07-21T12:42:25.971Z", "logs": [{"timestamp": "2025-07-21T12:42:25.971Z", "type": "test", "message": "Starting test: Project Analytics - Test project performance analytics and insights"}, {"timestamp": "2025-07-21T12:42:26.136Z", "type": "info", "message": "Project analytics data retrieved successfully"}, {"timestamp": "2025-07-21T12:42:26.136Z", "type": "info", "message": "Project analytics fields: total, byStatus, byCategory, projects"}, {"timestamp": "2025-07-21T12:42:26.385Z", "type": "error", "message": "Request failed: GET /analytics/projects/687e3205b817f2dcbff7a883 - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:26.386Z", "type": "info", "message": "Individual project analytics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:26.469Z", "type": "error", "message": "Request failed: GET /analytics/projects/trends - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:26.469Z", "type": "info", "message": "Project trends analytics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:26.553Z", "type": "error", "message": "Request failed: GET /analytics/projects/categories - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:26.554Z", "type": "info", "message": "Project category analytics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:26.555Z", "type": "success", "message": "Test PASSED: Project Analytics"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:26.555Z", "duration": 584}, {"name": "Revenue Tracking", "description": "Test revenue and earnings tracking functionality", "startTime": "2025-07-21T12:42:26.555Z", "logs": [{"timestamp": "2025-07-21T12:42:26.555Z", "type": "test", "message": "Starting test: Revenue Tracking - Test revenue and earnings tracking functionality"}, {"timestamp": "2025-07-21T12:42:26.641Z", "type": "error", "message": "Request failed: GET /analytics/revenue - Request failed with status code 403"}, {"timestamp": "2025-07-21T12:42:26.641Z", "type": "error", "message": "Test failed: Request failed with status code 403"}, {"timestamp": "2025-07-21T12:42:26.642Z", "type": "error", "message": "Test FAILED: Revenue Tracking"}], "passed": false, "errors": ["Request failed with status code 403"], "endTime": "2025-07-21T12:42:26.641Z", "duration": 86}, {"name": "User Engagement", "description": "Test user activity and engagement metrics", "startTime": "2025-07-21T12:42:26.643Z", "logs": [{"timestamp": "2025-07-21T12:42:26.643Z", "type": "test", "message": "Starting test: User Engagement - Test user activity and engagement metrics"}, {"timestamp": "2025-07-21T12:42:26.727Z", "type": "error", "message": "Request failed: GET /analytics/user/activity - Request failed with status code 500"}, {"timestamp": "2025-07-21T12:42:26.727Z", "type": "error", "message": "Test failed: Request failed with status code 500"}, {"timestamp": "2025-07-21T12:42:26.728Z", "type": "error", "message": "Test FAILED: User Engagement"}], "passed": false, "errors": ["Request failed with status code 500"], "endTime": "2025-07-21T12:42:26.727Z", "duration": 84}, {"name": "Performance Metrics", "description": "Test system and user performance metrics", "startTime": "2025-07-21T12:42:26.729Z", "logs": [{"timestamp": "2025-07-21T12:42:26.729Z", "type": "test", "message": "Starting test: Performance Metrics - Test system and user performance metrics"}, {"timestamp": "2025-07-21T12:42:26.877Z", "type": "info", "message": "System performance metrics retrieved successfully"}, {"timestamp": "2025-07-21T12:42:26.878Z", "type": "info", "message": "System metrics: responseTime, throughput, errorRate, uptime, memoryUsage, cpuUsage"}, {"timestamp": "2025-07-21T12:42:26.962Z", "type": "error", "message": "Request failed: GET /analytics/user/performance - Request failed with status code 500"}, {"timestamp": "2025-07-21T12:42:26.962Z", "type": "error", "message": "Test failed: Request failed with status code 500"}, {"timestamp": "2025-07-21T12:42:26.963Z", "type": "error", "message": "Test FAILED: Performance Metrics"}], "passed": false, "errors": ["Request failed with status code 500"], "endTime": "2025-07-21T12:42:26.962Z", "duration": 233}, {"name": "Analytics Filtering", "description": "Test filtering and date range functionality in analytics", "startTime": "2025-07-21T12:42:26.963Z", "logs": [{"timestamp": "2025-07-21T12:42:26.963Z", "type": "test", "message": "Starting test: Analytics Filtering - Test filtering and date range functionality in analytics"}, {"timestamp": "2025-07-21T12:42:27.238Z", "type": "info", "message": "Analytics for Last 7 days retrieved successfully"}, {"timestamp": "2025-07-21T12:42:27.591Z", "type": "info", "message": "Analytics for Last 30 days retrieved successfully"}, {"timestamp": "2025-07-21T12:42:27.870Z", "type": "info", "message": "Analytics for Last 3 months retrieved successfully"}, {"timestamp": "2025-07-21T12:42:28.225Z", "type": "info", "message": "Analytics for Last year retrieved successfully"}, {"timestamp": "2025-07-21T12:42:28.513Z", "type": "info", "message": "Custom date range analytics retrieved successfully"}, {"timestamp": "2025-07-21T12:42:28.695Z", "type": "info", "message": "Category-filtered analytics retrieved successfully"}, {"timestamp": "2025-07-21T12:42:28.857Z", "type": "info", "message": "Status-filtered analytics retrieved successfully"}, {"timestamp": "2025-07-21T12:42:28.857Z", "type": "success", "message": "Test PASSED: Analytics Filtering"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:28.857Z", "duration": 1894}, {"name": "Data Export", "description": "Test analytics data export functionality", "startTime": "2025-07-21T12:42:28.858Z", "logs": [{"timestamp": "2025-07-21T12:42:28.858Z", "type": "test", "message": "Starting test: Data Export - Test analytics data export functionality"}, {"timestamp": "2025-07-21T12:42:28.939Z", "type": "error", "message": "Request failed: GET /analytics/export?format=csv&type=projects - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:28.940Z", "type": "info", "message": "CSV export not implemented yet"}, {"timestamp": "2025-07-21T12:42:29.023Z", "type": "error", "message": "Request failed: GET /analytics/export?format=json&type=revenue - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:29.024Z", "type": "info", "message": "JSON export not implemented yet"}, {"timestamp": "2025-07-21T12:42:29.107Z", "type": "error", "message": "Request failed: POST /analytics/reports/generate - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:29.107Z", "type": "info", "message": "PDF report generation not implemented yet"}, {"timestamp": "2025-07-21T12:42:29.108Z", "type": "success", "message": "Test PASSED: Data Export"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:29.108Z", "duration": 250}, {"name": "Real-time Updates", "description": "Test real-time analytics updates and live data", "startTime": "2025-07-21T12:42:29.108Z", "logs": [{"timestamp": "2025-07-21T12:42:29.108Z", "type": "test", "message": "Starting test: Real-time Updates - Test real-time analytics updates and live data"}, {"timestamp": "2025-07-21T12:42:29.208Z", "type": "error", "message": "Request failed: GET /analytics/realtime - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:29.209Z", "type": "info", "message": "Real-time analytics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:29.345Z", "type": "error", "message": "Request failed: GET /analytics/live/metrics - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:29.345Z", "type": "info", "message": "Live metrics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:29.430Z", "type": "error", "message": "Request failed: POST /analytics/refresh - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:29.431Z", "type": "info", "message": "Analytics refresh endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:29.432Z", "type": "success", "message": "Test PASSED: Real-time Updates"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:29.432Z", "duration": 324}]}, {"name": "Admin Panel", "icon": "👑", "total": 8, "passed": 7, "failed": 1, "successRate": "87.50", "tests": [{"name": "User Management", "description": "Test user management and administration features", "startTime": "2025-07-21T12:42:29.764Z", "logs": [{"timestamp": "2025-07-21T12:42:29.764Z", "type": "test", "message": "Starting test: User Management - Test user management and administration features"}, {"timestamp": "2025-07-21T12:42:29.921Z", "type": "info", "message": "Found 9 total users in system"}, {"timestamp": "2025-07-21T12:42:29.921Z", "type": "info", "message": "User structure validation passed"}, {"timestamp": "2025-07-21T12:42:29.922Z", "type": "info", "message": "User role distribution: {\"client\":3,\"vendor\":5,\"admin\":1}"}, {"timestamp": "2025-07-21T12:42:30.049Z", "type": "info", "message": "User search returned 1 results"}, {"timestamp": "2025-07-21T12:42:30.210Z", "type": "info", "message": "Found 5 vendor users"}, {"timestamp": "2025-07-21T12:42:30.258Z", "type": "error", "message": "Request failed: GET /admin/users - Request failed with status code 403"}, {"timestamp": "2025-07-21T12:42:30.259Z", "type": "info", "message": "User management correctly restricted to admin users"}, {"timestamp": "2025-07-21T12:42:30.260Z", "type": "success", "message": "Test PASSED: User Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:30.260Z", "duration": 496}, {"name": "Role Assignment", "description": "Test user role management and permissions", "startTime": "2025-07-21T12:42:30.261Z", "logs": [{"timestamp": "2025-07-21T12:42:30.261Z", "type": "test", "message": "Starting test: Role Assignment - Test user role management and permissions"}, {"timestamp": "2025-07-21T12:42:30.386Z", "type": "info", "message": "Testing role assignment for user: <PERSON> (current role: vendor)"}, {"timestamp": "2025-07-21T12:42:30.528Z", "type": "info", "message": "Role assignment endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:30.573Z", "type": "error", "message": "Request failed: GET /admin/roles/permissions - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:30.573Z", "type": "info", "message": "Role permissions endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:30.620Z", "type": "error", "message": "Request failed: PUT /admin/users/bulk/role - Request failed with status code 500"}, {"timestamp": "2025-07-21T12:42:30.620Z", "type": "info", "message": "Bulk role assignment endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:30.621Z", "type": "success", "message": "Test PASSED: Role Assignment"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:30.621Z", "duration": 360}, {"name": "Platform Analytics", "description": "Test platform-wide analytics and reporting", "startTime": "2025-07-21T12:42:30.622Z", "logs": [{"timestamp": "2025-07-21T12:42:30.622Z", "type": "test", "message": "Starting test: Platform Analytics - Test platform-wide analytics and reporting"}, {"timestamp": "2025-07-21T12:42:30.665Z", "type": "error", "message": "Request failed: GET /admin/analytics/overview - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:30.666Z", "type": "error", "message": "Test failed: Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:30.668Z", "type": "error", "message": "Test FAILED: Platform Analytics"}], "passed": false, "errors": ["Request failed with status code 404"], "endTime": "2025-07-21T12:42:30.666Z", "duration": 44}, {"name": "System Configuration", "description": "Test system settings and configuration management", "startTime": "2025-07-21T12:42:30.668Z", "logs": [{"timestamp": "2025-07-21T12:42:30.668Z", "type": "test", "message": "Starting test: System Configuration - Test system settings and configuration management"}, {"timestamp": "2025-07-21T12:42:30.712Z", "type": "error", "message": "Request failed: GET /admin/config - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:30.712Z", "type": "info", "message": "System configuration endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:30.755Z", "type": "error", "message": "Request failed: PUT /admin/config - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:30.756Z", "type": "info", "message": "System configuration update endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:30.828Z", "type": "info", "message": "Feature flags retrieved successfully"}, {"timestamp": "2025-07-21T12:42:30.829Z", "type": "info", "message": "Available features: realTimeMessaging, advancedAnalytics, documentVersioning, aiRecommendations, multiLanguageSupport"}, {"timestamp": "2025-07-21T12:42:30.873Z", "type": "info", "message": "Feature flags updated successfully"}, {"timestamp": "2025-07-21T12:42:30.917Z", "type": "info", "message": "System limits retrieved successfully"}, {"timestamp": "2025-07-21T12:42:30.917Z", "type": "success", "message": "Test PASSED: System Configuration"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:30.917Z", "duration": 249}, {"name": "Content Moderation", "description": "Test content moderation and security features", "startTime": "2025-07-21T12:42:30.919Z", "logs": [{"timestamp": "2025-07-21T12:42:30.919Z", "type": "test", "message": "Starting test: Content Moderation - Test content moderation and security features"}, {"timestamp": "2025-07-21T12:42:30.961Z", "type": "error", "message": "Request failed: GET /admin/moderation/flagged - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:30.961Z", "type": "info", "message": "Flagged content endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.006Z", "type": "error", "message": "Request failed: GET /admin/moderation/flagged - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.007Z", "type": "info", "message": "Content approval endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.052Z", "type": "info", "message": "Found 1 user reports"}, {"timestamp": "2025-07-21T12:42:31.156Z", "type": "info", "message": "Content scan initiated successfully"}, {"timestamp": "2025-07-21T12:42:31.200Z", "type": "error", "message": "Request failed: GET /admin/moderation/rules - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.201Z", "type": "info", "message": "Content filtering rules endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.203Z", "type": "success", "message": "Test PASSED: Content Moderation"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:31.203Z", "duration": 284}, {"name": "Security Management", "description": "Test security monitoring and management features", "startTime": "2025-07-21T12:42:31.204Z", "logs": [{"timestamp": "2025-07-21T12:42:31.204Z", "type": "test", "message": "Starting test: Security Management - Test security monitoring and management features"}, {"timestamp": "2025-07-21T12:42:31.247Z", "type": "error", "message": "Request failed: GET /admin/security/logs - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.248Z", "type": "info", "message": "Security logs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.291Z", "type": "error", "message": "Request failed: GET /admin/security/failed-logins - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.292Z", "type": "info", "message": "Failed logins endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.336Z", "type": "error", "message": "Request failed: GET /admin/security/blocked-ips - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.337Z", "type": "info", "message": "Blocked IPs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.400Z", "type": "error", "message": "Request failed: GET /admin/security/alerts - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.400Z", "type": "info", "message": "Security alerts endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.401Z", "type": "success", "message": "Test PASSED: Security Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:31.401Z", "duration": 197}, {"name": "System Monitoring", "description": "Test system health and performance monitoring", "startTime": "2025-07-21T12:42:31.402Z", "logs": [{"timestamp": "2025-07-21T12:42:31.402Z", "type": "test", "message": "Starting test: System Monitoring - Test system health and performance monitoring"}, {"timestamp": "2025-07-21T12:42:31.445Z", "type": "error", "message": "Request failed: GET /admin/system/health - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.446Z", "type": "info", "message": "System health endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.489Z", "type": "error", "message": "Request failed: GET /admin/system/performance - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.490Z", "type": "info", "message": "Performance metrics endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.533Z", "type": "error", "message": "Request failed: GET /admin/system/errors - Request failed with status code 404"}, {"timestamp": "2025-07-21T12:42:31.534Z", "type": "info", "message": "Error logs endpoint not implemented yet"}, {"timestamp": "2025-07-21T12:42:31.577Z", "type": "info", "message": "System status retrieved successfully"}, {"timestamp": "2025-07-21T12:42:31.577Z", "type": "success", "message": "Test PASSED: System Monitoring"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:31.577Z", "duration": 175}, {"name": "Data Management", "description": "Test data backup, export, and maintenance features", "startTime": "2025-07-21T12:42:31.578Z", "logs": [{"timestamp": "2025-07-21T12:42:31.578Z", "type": "test", "message": "Starting test: Data Management - Test data backup, export, and maintenance features"}, {"timestamp": "2025-07-21T12:42:31.622Z", "type": "info", "message": "Data export initiated with ID: export_1753101751624"}, {"timestamp": "2025-07-21T12:42:31.664Z", "type": "info", "message": "Found 2 backup records"}, {"timestamp": "2025-07-21T12:42:31.727Z", "type": "info", "message": "Data cleanup initiated successfully"}, {"timestamp": "2025-07-21T12:42:31.966Z", "type": "info", "message": "Database statistics retrieved successfully"}, {"timestamp": "2025-07-21T12:42:31.967Z", "type": "success", "message": "Test PASSED: Data Management"}], "passed": true, "errors": [], "endTime": "2025-07-21T12:42:31.966Z", "duration": 388}]}], "generatedAt": "2025-07-21T12:42:31.973Z"}