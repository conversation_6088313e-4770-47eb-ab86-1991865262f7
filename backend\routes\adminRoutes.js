import express from 'express';
import {
  getAdminUsers,
  updateUserRole,
  getAdminAnalytics,
  getSystemFeatures,
  updateSystemFeatures,
  getSystemLimits,
  updateSystemLimits,
  getUserReports,
  scanContent,
  getSystemStatus,
  exportData,
  getBackupStatus,
  cleanupData,
  getDatabaseStats
} from '../controllers/adminController.js';
import { authMiddleware, adminMiddleware } from '../middleware/authMiddleware.js';

const router = express.Router();

// All admin routes require authentication and admin role
router.use(authMiddleware);
router.use(adminMiddleware);

// User Management
router.get('/users', getAdminUsers);
router.put('/users/:userId/role', updateUserRole);

// Analytics
router.get('/analytics/users/growth', getAdminAnalytics);
router.get('/analytics/revenue', getAdminAnalytics);

// System Configuration
router.get('/features', getSystemFeatures);
router.put('/features', updateSystemFeatures);
router.get('/limits', getSystemLimits);
router.put('/limits', updateSystemLimits);

// Content Moderation
router.get('/reports/users', getUserReports);
router.post('/moderation/scan', scanContent);

// System Monitoring
router.get('/system/status', getSystemStatus);

// Data Management
router.post('/data/export', exportData);
router.get('/data/backups', getBackupStatus);
router.post('/data/cleanup', cleanupData);
router.get('/data/statistics', getDatabaseStats);

export default router;
